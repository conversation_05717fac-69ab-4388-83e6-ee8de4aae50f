/// 这个文件存放一些全局工具函数，不要引入其他模块
import config from '@/config';
import { getLoneShortLink } from '@/api/other.js';
/**
 * 判断数据是否有效
 *
 * @param {Object} data 需要验证的数据
 */
export function dataIsValid(data) {
  return data != null && data != undefined;
}

/**
 * 判断数组是否非空
 *
 * @param {Object} arr 需要验证的数组
 */
export function arrayNotEmpty(arr) {
  let r = dataIsValid(arr);
  return r && Array.isArray(arr) && arr.length > 0;
}

/**
 *	判断对象是否非空
 *
 * @param {Object} obj 需要验证的对象(map)
 */
export function objNotEmpty(obj) {
  let r = dataIsValid(obj);
  return r && Object.keys(obj).length > 0;
}

/**
 * 判断字符串是否非空
 *
 * @param {String} str 需要验证的字符串
 */
export function stringNotEmpty(str) {
  let r = dataIsValid(str);
  return r && str.length > 0;
}

/**
 * 将字符串非空处理，字符串无效，则返回 "暂无"
 *
 * @param {String} str 需要处理的字符串
 */
export function stringValid(str) {
  if (stringNotEmpty(str)) return str;
  return '暂无';
}

/**
 * 将数组非空处理，数组无效，则返回 []
 *
 * @param {Object} arr 需要处理的数组
 */
export function arrayValid(arr) {
  if (arrayNotEmpty(arr)) return arr;
  return [];
}

/**
 * 将对象非空处理，对象无效，则返回 {}
 *
 * @param {Object} obj 需要处理的对象
 */
export function objectValid(obj) {
  if (objNotEmpty(obj)) return obj;
  return {};
}

/**
 * 将数字非空处理，数字无效，则返回 0
 *
 * @param {Number} num 需要处理的数字
 */
export function numberValid(num) {
  if (dataIsValid(num)) return num;
  return 0;
}
//通过身份证获取生日
export function idCardGetBri(idCard) {
  var birthday = '';
  if (idCard != null && idCard != '') {
    if (idCard.length == 15) {
      birthday = '19' + idCard.slice(6, 12);
    } else if (idCard.length == 18) {
      birthday = idCard.slice(6, 14);
    }
    birthday = birthday.replace(/(.{4})(.{2})/, '$1-$2-');
    //通过正则表达式来指定输出格式为:1990-01-01
  }
  return birthday;
}
// 通过身份证获取年龄
export function getAgeFromIDCard(idCard) {
  // 通过身份证号解析出生日期
  const birthday = idCard.substring(6, 14); // 身份证号中出生日期所在位置
  const year = birthday.substring(0, 4);
  const month = birthday.substring(4, 6);
  const day = birthday.substring(6, 8);

  // 计算年龄
  const today = new Date();
  const birthDate = new Date(year, month - 1, day);
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  if (
    monthDiff < 0 ||
    (monthDiff === 0 && today.getDate() < birthDate.getDate())
  ) {
    age--;
  }
  return age;
}
// 通过生日获取年龄
export function briGetAge(birthday) {
  var age = '';
  if (birthday != null && birthday != '') {
    var birthDate = new Date(birthday);
    var nowDate = new Date();
    var yearDiff = nowDate.getFullYear() - birthDate.getFullYear();
    var monthDiff = nowDate.getMonth() - birthDate.getMonth();
    var dayDiff = nowDate.getDate() - birthDate.getDate();
    if (monthDiff < 0 || (monthDiff == 0 && dayDiff < 0)) {
      yearDiff--;
    }
    age = yearDiff;
  }
  return age;
}
//通过身份证获取性别
export function idCardGetSex(idCard) {
  var sexStr = '';
  if (parseInt(idCard.slice(-2, -1)) % 2 == 1) {
    sexStr = '男';
  } else {
    sexStr = '女';
  }
  return sexStr;
}

// 判断身份证是否输入正确
export function idCardTrue(id) {
  // const sfzReg = /^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/
  // return sfzReg.test(id)
  // 1 "验证通过!", 0 //校验不通过
  var format =
    /^(([1][1-5])|([2][1-3])|([3][1-7])|([4][1-6])|([5][0-4])|([6][1-5])|([7][1])|([8][1-2]))\d{4}(([1][9]\d{2})|([2]\d{3}))(([0][1-9])|([1][0-2]))(([0][1-9])|([1-2][0-9])|([3][0-1]))\d{3}[0-9xX]$/;
  //号码规则校验
  if (!format.test(id)) {
    return false;
  }
  //区位码校验
  //出生年月日校验   前正则限制起始年份为1900;
  var year = id.substr(6, 4), //身份证年
    month = id.substr(10, 2), //身份证月
    date = id.substr(12, 2), //身份证日
    time = Date.parse(month + '-' + date + '-' + year), //身份证日期时间戳date
    now_time = Date.parse(new Date()), //当前时间戳
    dates = new Date(year, month, 0).getDate(); //身份证当月天数
  if (time > now_time || date > dates) {
    return false;
  }
  //校验码判断
  var c = new Array(7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2); //系数
  var b = new Array('1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'); //校验码对照表
  var id_array = id.split('');
  var sum = 0;
  for (var k = 0; k < 17; k++) {
    sum += parseInt(id_array[k]) * parseInt(c[k]);
  }
  if (id_array[17].toUpperCase() != b[sum % 11].toUpperCase()) {
    return false;
  }
  return true;
}

// 通过url获取后面参数转换为对象
export function getUrlParams(url) {
  const Params = {};
  if (url.indexOf('?') > 0) {
    //判断是否有query
    let params = url.slice(url.indexOf('?') + 1); //截取出query
    const paradises = params.split('&'); //分割键值对
    for (const param of paradises) {
      let a = param.split('=');
      Object.assign(Params, {
        [a[0]]: a[1],
      }); //将键值对封装成对象
    }
  }
  return Params;
}

// js分组
/**
 * 对数组进行分组
 * @param {Array} arr 需要分组的数组
 * @param {String} key 分组的键名
 * @param {Array} fields 可选参数，指定返回数据中需要包含的字段，如 ['UserId', 'Type']
 * @returns {Array} 分组后的数组
 */
export function ItemGroupBy(arr, key, fields) {
  let newArr = [],
    types = {},
    i,
    j,
    cur;
  for (i = 0, j = arr.length; i < j; i++) {
    cur = arr[i];
    if (!(cur[key] in types)) {
      types[cur[key]] = {
        type: cur[key],
        data: [],
      };
      newArr.push(types[cur[key]]);
    }

    // 如果指定了字段，则只返回指定的字段
    if (fields && Array.isArray(fields) && fields.length > 0) {
      let filteredItem = {};
      fields.forEach((field) => {
        if (cur.hasOwnProperty(field)) {
          filteredItem[field] = cur[field];
        }
      });
      types[cur[key]].data.push(filteredItem);
    } else {
      // 没有指定字段，返回完整数据（原有行为）
      types[cur[key]].data.push(cur);
    }
  }
  return newArr;
}

/**
 *	对数组进行时间排序
 *
 * @param {String} property 排序传入的key
 * @param {Boolean} bol true时是升序，false为降序
 */
export function dateData(property, bol) {
  return function (a, b) {
    var value1 = a[property];
    var value2 = b[property];
    if (bol) {
      // 升序
      return Date.parse(value1) - Date.parse(value2);
    } else {
      // 降序
      return Date.parse(value2) - Date.parse(value1);
    }
  };
}
/**
 * 对字符串进行转义
 */
export const escapeRegExp = (string) => {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'); // $& 表示与被匹配的字符相匹配的子串
};
/**
 * 简单校验手机号是否正确
 * @param {Object} phoneNumber
 */
export const validatePhoneNumber = (phoneNumber) => {
  var reg = /^[1][3-9]\d{9}$/;
  return reg.test(phoneNumber);
};
/**
 * @param {mapName} string 场景值
 * @param {subscribeType} string 长期消息还是短期消息 默认长期
 * @param {cb} Function 回调函数
 */
export const popUpMsgTempList = (mapName, subscribeType = 'LongTerm', cb) => {
  const app = getApp();
  const msgTemplatesList = app.globalData.msgTemplates;
  console.log('msgTemplatesList', msgTemplatesList);
  const mapNameNewList = msgTemplatesList[mapName];
  console.log('mapNameNewList', mapNameNewList);
  let itemTemplates = [];
  if (mapNameNewList instanceof Array && mapNameNewList.length > 0) {
    // newArrTemplatesList = this.ItemGroupBy(mapNameNewList, 'SubscribeType')
    itemTemplates = mapNameNewList.filter(
      (e) => e.SubscribeType === subscribeType
    );
  }
  const templateIds = [];
  console.log('itemTemplates', itemTemplates);
  if (itemTemplates && itemTemplates.length > 0) {
    itemTemplates.forEach((e) => {
      templateIds.push(e.TemplateId);
    });
  }
  console.log('templateIds', templateIds);
  uni.requestSubscribeMessage({
    templateIds,
    complete: () => {
      if (cb) cb();
    },
  });
};
/**
 * js 两数求和
 */
export const sumOfTwoNumbers = (arg1, arg2) => {
  var r1, r2, m;
  try {
    r1 = arg1.toString().split('.')[1].length;
  } catch (e) {
    r1 = 0;
  }
  try {
    r2 = arg2.toString().split('.')[1].length;
  } catch (e) {
    r2 = 0;
  }
  m = Math.pow(10, Math.max(r1, r2));
  return (arg1 * m + arg2 * m) / m;
};

/** 对象转query字符串*/
export function toQueryString(obj, prefix) {
  const query = [];

  for (let key in obj) {
    if (obj.hasOwnProperty(key)) {
      const value = obj[key];
      const encodedKey = prefix
        ? `${prefix}[${encodeURIComponent(key)}]`
        : encodeURIComponent(key);

      if (typeof value === 'object' && value !== null) {
        // 递归处理嵌套对象
        query.push(toQueryString(value, encodedKey));
      } else {
        // 编码键值对并添加到查询数组
        query.push(`${encodedKey}=${encodeURIComponent(value)}`);
      }
    }
  }

  return query.join('&');
}
/** 通过短链接获取长链接 */
export const getLongLinkByShortLink = async (decodeUrl) => {
  try {
    const res = await getLoneShortLink(decodeUrl);
    if (res.Type === 200 && res.Data) {
      return res.Data;
    }
    uni.$log.error(
      `${config.envVersion}:短链接获取长链接失败 Type == 200 没有Data。 短链接是：${decodeUrl}`
    );
    return null;
  } catch (error) {
    uni.$log.error(
      `${config.envVersion}:短链接获取长链接失败 Type !== 200。 短链接是：${decodeUrl}; 返回的数据：${JSON.stringify(
        error
      )}`
    );
    return null;
  }
};
export const transformUrl = (str) => {
  // 找到最后一个斜杠的位置
  const lastSlashIndex = str.lastIndexOf('?');
  if (lastSlashIndex === -1) {
    return str; // 如果没有斜杠，直接返回原字符串
  }
  // 获取斜杠后的内容（不包含斜杠）
  const contentToMove = str.slice(lastSlashIndex + 1);
  // 删除斜杠后的内容（包括斜杠）
  const modifiedStr = str.slice(0, lastSlashIndex);
  // 返回修改后的字符串，最后添加删除的内容
  return (
    modifiedStr +
    '/' +
    contentToMove.split('=')[contentToMove.split('=').length - 1]
  );
};

/**
 * 检测麦克风权限
 * @param once 是否只检测一次，默认 true。如果设置为 false，当没有权限时，会提示用户去授予权限，操作完成后会再检查一次是否有权限
 **/
export async function checkMicAuth(once = true) {
  return new Promise(async (resolve) => {
    const setting = await wx.getSetting();
    console.log(
      'setting.authSetting.scope.record',
      setting.authSetting['scope.record']
    );
    if (setting.authSetting['scope.record'] == true) return resolve(true);

    if (once) {
      resolve(false);
    }
    uni.authorize({
      scope: 'scope.record',
      success: () => {
        resolve(true);
      },
      fail: () => {
        uni.showModal({
          content: '检测到您没打开录音功能权限，是否去设置打开？',
          confirmText: '确认',
          cancelText: '取消',
          success: (res) => {
            if (res.confirm) {
              uni.openSetting({
                success: (res) => {
                  // console.log(res.authSetting);
                  resolve(res.authSetting['scope.record']);
                },
              });
            } else {
              resolve(false);
            }
          },
          fail: () => resolve(false),
        });
      },
    });
  });
}
export const getUserEnterOptions = () => {
  const data = uni.getEnterOptionsSync();
  return {
    path: data.path,
    query: data.query,
    scene: data.scene,
  };
};

/**
 * px 和 rpx 转换
 *
 * @param {number} px 需要转换的px数值
 * @param {boolean} reverse 是否反转，默认 false；true 表示 rpx 转 px
 */
export let pxToRpx = (px, reverse = false) => {
  const windowInfo = uni.getWindowInfo();
  const scale = 750 / windowInfo.screenWidth;
  function _pxToRpx(px, reverse = false) {
    if (reverse) return px / scale;
    return px * scale;
  }
  pxToRpx = _pxToRpx;
  return _pxToRpx(px, reverse);
};

// 验证手机号是否合法
export const isValidPhone = (phone) => {
  return /^1\d{10}$/.test(phone);
};
