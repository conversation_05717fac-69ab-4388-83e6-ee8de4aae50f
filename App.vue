<script>
import config from '@/config.js';
import SessionState from '@/libs/util/session_state.js';
import { UserClientEvent } from '@/utils/eventKeys.js';
import { IMManager } from 'kfx-im';
import {
  initAfterLoginIn,
  accountAndPasswordLogin,
  phoneLogin,
} from '@/services/LoginService';
import IMContactInfoService from '@/services/im/im_contact_info_service.js';
import IMUploadFileService from '@/services/im/im_upload_file_service.js';
import { SignalChannel } from '@/uni_modules/kfx-signal_channel/js_sdk/index';
import ChatCacheManager from '@/libs/util/chat_cache_manager.js';
import { getSettingWithoutAuth } from '@/api/bff.js';
import { ItemGroupBy } from '@/utils/utils';
import { Completer } from '@/libs/util/Completer.js';
import { saveUserAuthen } from '@/services/userAuthen/index.js';
import { accountLogin, updateMark } from './api/passport';
import dayjs from 'dayjs';
import { DEFAULT_DYNAMIC_CONFIG } from './constants';
import logger from './services/logger';
import ProtectionManager from './services/ProtectionManager';

export default {
  /// 全局数据，如果需要把globalData的数据绑定到页面上，可在页面的onShow页面生命周期里进行变量重赋值。
  /// 这里不建议存放响应式数据
  globalData: {
    userInfo: {},
    token: '',
    openId: '',
    orgId: '',
    orgName: '',
    deptId: '',
    signalChannel: null,
    /**
     * 消息模板
     */
    msgTemplates: {},
    /**
     * 通过医生治疗师二维码或医院二维码进入
     * 医生治疗师 doc
     * 医院 org
     */
    via: '',
    PrivacyProtocol: {
      needAuthorization: true,
      title: '',
    },
    resolvePrivacyAuthorization: null,
    /**
     * 是否首页进入过计划页面
     */
    indexToPlan: false,
    dynamicConfig: DEFAULT_DYNAMIC_CONFIG,
  },
  async onLaunch(options) {
    console.log('App Launch', options);

    this.globalData.launchCompleter = new Completer();
    this.loadCache();
    this.initListener();
    this.checkMiniProgramUpdate();
    this.checkUserPrivacyProtocol();

    if (!this.globalData.refToken) {
      // 这里需要判定 refToken，因为端token 也有可能过期，但是无法刷新，导致提示登录过期
      await this.generateCommonToken();
    }
    await this.requestDynamicConfig();

    // 初始化会话管理模块
    const sessionState = SessionState.instance();
    // 初始化会话聊天信息缓存管理
    const chatManager = ChatCacheManager.instance();
    chatManager.getStorageChat();
    // 初始化signalChannel
    this.globalData.signalChannel = new SignalChannel(
      config.websocketUrl,
      () => this.globalData.token
    );
    // 初始化IM
    const imManager = IMManager.instance;
    IMManager.contactService = new IMContactInfoService();
    IMManager.uploadFileService = new IMUploadFileService();

    if (this.isLoggedIn()) {
      // initAfterLoginIn 中用到了 getAPP(),所以需要在下个周期执行
      setTimeout(async () => {
        console.log('初始化用户状态');
        const r = await initAfterLoginIn();
        console.log(`用户状态初始化成功${r ? '成功' : '失败'}`);
      }, 0);
    }
    this.globalData.launchCompleter.complete();
  },
  onShow(options) {
    console.log('App Show', options);
    if (this.isLoggedIn()) {
      this.$store.dispatch('message/syncMessageStatus');
      // store.dispatch('message/syncPrescriptionId');
    }
  },
  onHide: function () {
    console.log('App Hide');
    ChatCacheManager.instance().storageChat();
    uni.removeStorageSync('isOpenDialog');
  },
  onError(error) {
    this.$log.error(this.$envVersion, 'App onError', error);
  },
  onUnhandledRejection(error) {
    this.$log.error(this.$envVersion, `App onUnhandledRejection`, error);
  },
  onLastPageBackPress() {
    console.log('App LastPageBackPress');
  },
  onExit() {
    console.log('App Exit');
  },
  methods: {
    getBirthdayByAge(age) {
      const ageNumber = Number(age);
      return dayjs().subtract(ageNumber, 'year').format('YYYY-MM-DD');
    },
    /**
     * 检查小程序是否有新版本
     */
    checkMiniProgramUpdate() {
      const updateManager = uni.getUpdateManager();
      //判断是否有新的版本更新，此处返回的是true或者false
      updateManager.onCheckForUpdate(function (res) {
        console.log('检查版本是否更新', res.hasUpdate);
      });
      updateManager.onUpdateReady(function () {
        uni.showModal({
          title: '更新提示',
          content: '新版本已经准备好，是否重启应用？',
          success(res) {
            if (res.confirm) {
              // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
              updateManager.applyUpdate();
            }
          },
        });
      });
      updateManager.onUpdateFailed(function (res) {
        // 新的版本下载失败
        console.log('新版本更新失败', res);
      });
    },
    /**
     * 加载本地缓存
     */
    loadCache() {
      this.globalData.orgId = uni.getStorageSync('chooseOrgID');
      this.globalData.orgName = uni.getStorageSync('chooseOrgName');
      if (uni.getStorageSync('token')) {
        this.globalData.token = uni.getStorageSync('token');
        this.globalData.refToken = uni.getStorageSync('ref_token');
        this.globalData.openId = uni.getStorageSync('openID');
        this.globalData.userInfo = uni.getStorageSync('userInfoLoging');
        this.globalData.deptId = uni.getStorageSync('deptId');
      }

      if (!this.globalData.orgId && config.orgId) {
        const { orgId, orgName } = config;
        this.globalData.orgId = orgId;
        this.globalData.orgName = orgName;
        uni.setStorageSync('chooseOrgID', orgId);
        uni.setStorageSync('chooseOrgName', orgName);
      }
    },
    /**
     * 生成通用 token
     */
    async generateCommonToken() {
      let data = {
        appid: config.clientId,
        appsecret: 'secret',
      };
      const res = await accountLogin(data);
      console.log('generateCommonToken', res);
      if (res.Type === 200 && res.Data.access_token) {
        const token = res.Data.access_token;
        uni.setStorageSync('token', token);
        this.globalData.token = token;
      } else {
        console.error('生成通用 token失败');
      }
    },
    /**
     * 获取动态配置信息
     */
    async requestDynamicConfig() {
      const res = await getSettingWithoutAuth({
        Filter: {
          Category: 'MP-Patient',
        },
      });
      if (res.Type === 200) {
        console.info('设置动态配置', res);
        this.globalData.dynamicConfig = {
          ...DEFAULT_DYNAMIC_CONFIG,
          ...res.Data[0].Data,
        };
      } else {
        console.warn('获取动态配置失败', res);
      }

      const { grayVersion, relogin, keywordRegexp } =
        this.globalData.dynamicConfig;

      if (keywordRegexp) {
        uni.$inputValueRegExp = new RegExp(keywordRegexp);
      }
      if (grayVersion === config.version) {
        config.envVersion = 'trial';
        await this.generateCommonToken();
      }
      const accountInfo = uni.getAccountInfoSync();
      if (
        relogin &&
        !uni.getStorageSync('userReLogin') ===
          (accountInfo.miniProgram.version || true)
      ) {
        this.logout();
      }
    },
    /**
     * 检测用户隐私协议
     */
    checkUserPrivacyProtocol() {
      if (wx.getPrivacySetting) {
        wx.getPrivacySetting({
          success: (res) => {
            console.log(res);
            this.globalData.PrivacyProtocol.needAuthorization =
              res.needAuthorization;
            if (res.needAuthorization) {
              // 需要弹出隐私协议
              this.globalData.PrivacyProtocol.title = res.privacyContractName;
            }
          },
        });
      }
    },
    getAccountId() {
      return this.globalData.userInfo.Id;
    },
    /**
     * 判断是否已登录
     */
    isLoggedIn() {
      return Boolean(this.globalData.userInfo.Id);
    },
    isCertified() {
      return this.globalData.userInfo.user_audit_status == '2';
    },
    /**
     * 统一跳转到聊天页面
     * @param {string} id consultId
     */
    toChatPage(id) {
      uni.navigateTo({
        url: `/subPackChat/sessionChatPage?consultId=${id}`,
      });
    },
    initListener() {
      // 监听用户密码修改
      uni.$on(UserClientEvent.passwordChanged, () => {
        uni.showModal({
          showCancel: false,
          title: '温馨提示',
          content: '登录已失效',
          success: () => {
            this.logout();
            let pages = getCurrentPages();
            if (pages.length != 1 || pages[0].route != 'subGauge/transfer') {
              uni.switchTab({
                url: '/pages/index/index',
              });
            }
          },
        });
      });
      // 监听服务器是否让用户强制下线
      uni.$on(UserClientEvent.onCloseConnectionFromServerSide, async (data) => {
        this.logout();
        uni.showModal({
          showCancel: false,
          title: '温馨提示',
          content: data.message,
          success: () => {
            this.showMainPage();
          },
        });
      });

      if (config.videoCallEnable) {
        console.log('监听视频通话请求');
        // 监听视频通话是否打过来
        uni.$on(UserClientEvent.onVideoCall, (docInfo) => {
          uni.$log.info('小程序收到了医生打的视频电话', docInfo);
          let pages = getCurrentPages();
          let prePage = pages[pages.length - 1];
          if (prePage.route === 'subIM/videoCall') {
            return;
          }
          uni.navigateTo({
            url: '/subIM/videoCall?type=0&docInfo=' + docInfo,
          });
        });
      }
    },
    /**
     * 修改本地用户信息
     * @param {string } key
     * @param {*} value
     */
    changeUserInfo(key, value) {
      const userInfo = this.globalData.userInfo;
      if (userInfo) {
        if (key === 'idCard') {
          if (userInfo.UserCertificates.length > 0) {
            userInfo.UserCertificates[0].CertificateValue = value;
          } else {
            userInfo.UserCertificates.push({
              CertificateValue: value,
              CertificateType: 'idCard',
            });
          }
        } else {
          userInfo[key] = value;
          this.globalData.userInfo[key] = value;
        }
        uni.setStorageSync('userInfoLoging', userInfo);
      }
    },
    /**
     *	切换医院
     *
     * @param {{orgId: string, orgName: string,deptId?: string}} org {orgId: 机构Id, orgName: 机构名称,deptId: 科室 ID}
     * @param {Boolean} onlyMark 是否为用户标记该医院
     */
    async changeOrgAndMark(org, onlyMark = false) {
      console.log('changeOrgAndMark', org);
      const { orgId, orgName, deptId } = org;
      if (!orgId) {
        throw new Error('医院ID不能为空');
      }

      if (!onlyMark && this.globalData.orgId != orgId) {
        if (!orgName) {
          throw new Error('医院名称不能为空');
        }
        uni.setStorageSync('chooseOrgID', orgId);
        uni.setStorageSync('chooseOrgName', orgName);
        uni.setStorageSync('deptId', deptId ?? '');
        this.globalData.orgId = orgId;
        this.globalData.orgName = orgName;
        this.globalData.deptId = deptId ?? '';
      }

      if (this.isLoggedIn()) {
        await updateMark({
          UserId: this.globalData.userInfo.Id,
          OrganizationId: orgId,
          OrganizationEnv: 'yankang',
        });
      }
    },
    /**
     *	订阅消息
     *
     * @param {Function} cb 回调
     */
    subscribeMessage(cb, templateType = 'LaunchConsult', term = 'LongTerm') {
      const msgTemplateMap = this.globalData.msgTemplates;
      const templateList = msgTemplateMap[templateType];
      if (templateList) {
        let groups = [];
        if (templateList instanceof Array && templateList.length > 0) {
          groups = ItemGroupBy(templateList, 'SubscribeType');
        }
        const items = groups.find((e) => e.type === term);
        const tmplIds = [];
        if (items && items.data && items.data.length > 0) {
          items.data.forEach((e) => {
            tmplIds.push(e.TemplateId);
          });
        }
        uni.requestSubscribeMessage({
          tmplIds: tmplIds,
          success: (res) => {
            console.log(`${term}订阅消息`, res);
          },
          complete: () => {
            if (cb) cb();
          },
        });
      } else {
        cb();
      }
    },
    /**
     * 账号密码登录
     * @param {string} account
     * @param {string} password
     * @return {Promise<boolean>} true 成功 false 失败
     */
    async loginInWithAccountAndPassword(account, password) {
      uni.showLoading({
        title: '登录中，请稍后',
      });
      let r = false;
      try {
        r = await accountAndPasswordLogin(account, password);
      } catch (e) {
        logger.warn('登录异常', e);
      }
      uni.hideLoading();
      if (r) {
        ProtectionManager.setPassword(password);
      }
      return r;
    },
    /**
     * 微信手机号登录
     * @param {*} event 微信登录按键事件
     * @return {Promise<boolean>} true 成功 false 失败
     */
    async loginInWithWX(event) {
      return await phoneLogin(event);
    },
    /**
     * 登出
     */
    async logout() {
      const isLoggedInBefore = this.isLoggedIn();

      this.globalData.userInfo = {};
      this.globalData.openId = '';
      this.globalData.token = '';
      this.globalData.via = '';

      uni.removeStorageSync('openID');
      uni.removeStorageSync('token');
      uni.removeStorageSync('ref_token');
      uni.removeStorageSync('userInfoLoging');
      uni.removeStorageSync('account');
      uni.removeStorageSync('SGUser');

      this.globalData.orgId = '';
      this.globalData.orgName = '';
      uni.removeStorageSync('chooseOrgID');
      uni.removeStorageSync('chooseOrgName');
      uni.removeStorageSync('deptId');

      if (isLoggedInBefore) {
        // IM登出
        const imManager = IMManager.instance;
        imManager.logout();

        // 重置会话列表数据
        const sessionState = SessionState.instance();
        sessionState.reset();

        // console.log(this, this.$scope);
        if (this.$store) {
          this.$store.commit('message/updatePrescriptionId', '');
          this.$store.commit('message/updateUnreadMessageCount', 0);
        } else {
          // 通过 getAPP() 获取到的 app 实例不是 Vue 组件，需要调用 .$vm 获取 Vue 组件实例
          this.$vm.$store.commit('message/updatePrescriptionId', '');
          this.$vm.$store.commit('message/updateUnreadMessageCount', 0);
        }

        const signalChannel = this.globalData.signalChannel;

        try {
          await signalChannel.cleanMethods();
          await signalChannel.stop();
        } catch (e) {
          console.error(e.message);
        }
      }
      await this.generateCommonToken();
    },
    /**
     * 是否 launch 完成
     */
    async isLaunchFinish() {
      return this.globalData.launchCompleter.future();
    },
    /**
     * 用户认证流程
     * @param {Object} data
     */
    async saveAuthen(data, showError = false) {
      saveUserAuthen(data, showError);
    },
    /**
     * @param {Object} [options]
     * @param {boolean} [options.reLaunch]
     * @param {string} [options.redirect]
     */
    openLoginPage(options) {
      const reLaunch = options?.reLaunch ?? false;
      let url = '/subGauge/login';
      if (options?.redirect) {
        url += '?redirect=' + encodeURIComponent(options.redirect);
      }
      // console.debug('打开登录页面',url);
      if (reLaunch) {
        uni.reLaunch({
          url,
        });
      } else {
        uni.navigateTo({
          url,
        });
      }
    },
    showMainPage() {
      uni.reLaunch({
        url: '/pages/index/index',
      });
    },
  },
};
</script>

<style lang="scss">
@import '@/uni_modules/uview-ui/index.scss';
@import '@/static/font/iconfont.css';

/* 小程序整体为灰白色 用于大型的追悼日 */
/* page {
		filter: grayscale(100%);
		-webkit-filter: grayscale(100%);
		-moz-filter: grayscale(100%);
		-ms-filter: grayscale(100%);
		-o-filter: grayscale(100%);
		filter: progid:DXImageTransform.Microsoft.BasicImage(grayscale=1);
		-webkit-filter: grayscale(1);
	} */

/* @import "/wxcomponents/vant/dist/common/index.wxss"; */
/deep/ .u-skeleton {
  padding: 20rpx !important;
}

/deep/ .u-modal__content {
  text-align: center !important;
}

/deep/ .u-upload__button {
  background-color: white !important;
}

/deep/ .u-reset-button {
  line-height: 16px !important;
}

/deep/ .u-action-sheet {
  max-height: 200px !important;
  overflow-y: auto !important;
}

/deep/ .u-reset-button {
  line-height: 10px;
}

/deep/ .u-checkbox-group--row {
  flex-flow: wrap;
  margin-top: 6rpx;
}

/deep/ .u-radio-group {
  flex-flow: wrap;
}

/deep/ .u-checkbox {
  margin-right: 40rpx;
}

/deep/ .u-radio {
  margin-right: 40rpx;
}

/deep/ .u-search {
  padding: 10px;
  background-color: white;
}

.press-down-style {
  opacity: 0.5;
}

.text-max1 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  word-break: break-all;
}

.text-max2 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  word-break: break-all;
}

.btn-agree {
  border-bottom-left-radius: 3px;
  border-bottom-right-radius: 3px;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
  color: #fff;
  background-color: #29b7a3;
  border-color: #ebedf0;
  border-width: 1px;
  border-style: solid;
  padding: 0 12px;
  font-size: 14px;
  height: 40px;
  position: relative;
  align-items: center;
  justify-content: center;
  display: flex;
  flex-direction: row;
  box-sizing: border-box;
  flex-direction: row;
  width: 40%;
  line-height: 16px !important;
}

.refuse-agree {
  border-bottom-left-radius: 3px;
  border-bottom-right-radius: 3px;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
  color: #000000;
  background-color: #f7f7f7;
  border-color: #ebedf0;
  border-width: 1px;
  border-style: solid;
  padding: 0 12px;
  font-size: 14px;
  height: 40px;
  position: relative;
  align-items: center;
  justify-content: center;
  display: flex;
  flex-direction: row;
  box-sizing: border-box;
  flex-direction: row;
  width: 40%;
  line-height: 16px !important;
}

.bomm-btn-style {
  height: 80px;
}

.container {
  background-color: #f7f7f7;
  height: 100vh;
}

.display-style {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.container-bomBtn {
  width: calc(100% - 64rpx);
  text-align: center;
  background: #29b7a3;
  border-radius: 16rpx;
  font-weight: 400;
  font-size: 0.88rem;
  color: #ffffff;
  padding: 22rpx 0;
  position: fixed;
  bottom: 68rpx;
  left: 50%;
  transform: translateX(-50%);
  z-index: 999;
}

.display-style1 {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.display-style2 {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.display-style3 {
  display: flex;
  justify-content: flex-start;
}

.display-style4 {
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
}

.flex-start-center-column {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-direction: column;
}

.flex-center-start-column {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  flex-direction: column;
}

.flex-start-end-column {
  display: flex;
  justify-content: flex-start;
  align-items: flex-end;
  flex-direction: column;
}

.flex-start-start-column {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
}

.flex-center-stretch-column {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: stretch;
}

.flex-between-center {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-center-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-end-center {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.flex-start-center {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.flex-start-start {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
}

.flex-start-center-wrap {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: wrap;
}

.p-style {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  overflow: hidden;
}

.p-style2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  /* 行数 */
  -webkit-box-orient: vertical;
}

.p-style3 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  /* 行数 */
  -webkit-box-orient: vertical;
}

.p-style11 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  /* 行数 */
  -webkit-box-orient: vertical;
}

.p-style1 {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.btnButtomStyle {
  color: #ffffff;
  width: 90%;
  bottom: 30px;
  position: fixed;
  left: 50%;
  transform: translateX(-50%);
  z-index: 999;
  background: #14b593;
  text-align: center;
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 40rpx;
  box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(41, 183, 163, 0.08);
  font-size: 32rpx;
}

.container-btn2Box {
  width: 90%;
  bottom: 30px;
  position: fixed;
  left: 50%;
  transform: translateX(-50%);
  z-index: 999;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

view,
scroll-view,
swiper,
swiper-item,
cover-view,
cover-image,
icon,
text,
rich-text,
progress,
button,
checkbox,
form,
input,
label,
radio,
slider,
switch,
textarea,
navigator,
audio,
camera,
image,
video {
  box-sizing: border-box;
}

/* 骨架屏替代方案 */
.Skeleton {
  background: #f3f3f3;
  padding: 20upx 0;
  border-radius: 8upx;
}

/* 图片载入替代方案 */
.image-wrapper {
  font-size: 0;
  background: #f3f3f3;
  border-radius: 4px;

  image {
    width: 100%;
    height: 100%;
    transition: 0.6s;
    opacity: 0;

    &.loaded {
      opacity: 1;
    }
  }
}

.clamp {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
}

.common-hover {
  background: #f5f5f5;
}

/*边框*/
.b-b:after,
.b-t:after {
  position: absolute;
  z-index: 3;
  left: 0;
  right: 0;
  height: 0;
  content: '';
  transform: scaleY(0.5);
  border-bottom: 1px solid $border-color-base;
}

.b-b:after {
  bottom: 0;
}

.b-t:after {
  top: 0;
}

/* button样式改写 */
uni-button,
button {
  height: 80upx;
  line-height: 80upx;
  font-size: $font-lg + 2upx;
  font-weight: normal;

  &.no-border:before,
  &.no-border:after {
    border: 0;
  }
}

uni-button[type='default'],
button[type='default'] {
  color: $font-color-dark;
}

/* input 样式 */
.input-placeholder {
  color: #999999;
}

.placeholder {
  color: #999999;
}

.navigator-item-tag {
  background-color: white;
  position: fixed;
  right: 0;
  top: 45%;
  width: 128rpx;
  height: 114rpx;
  border-top-left-radius: 57rpx;
  border-bottom-left-radius: 57rpx;
  box-shadow: 0 0 18rpx rgba(0, 0, 0, 0.16);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 10000;
}

.navigator-item-tag image {
  width: 40rpx;
  height: 40rpx;
}

.navigator-item-tag text {
  font-size: 26rpx;
  color: #999999;
  font-weight: 500;
  margin-top: 6rpx;
}

.hover-class {
  opacity: 0.7;
}

.title-name-style {
  font-weight: 600;
  font-size: 28rpx;
  color: #323233;
  line-height: 40rpx;
}

.label-name-style {
  font-weight: 400;
  font-size: 24rpx;
  color: #646466;
  line-height: 34rpx;
}

page {
  height: 100%;
  background-color: #f7f7f7;
}
</style>
