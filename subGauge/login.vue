<template>
  <view class="container">
    <view class="container-top">
      <u--image
        :showLoading="true"
        src="/static/images/yankang-icon.png"
        width="160px"
        height="160px"
      >
      </u--image>
    </view>

    <u-button
      v-if="!agrCheck"
      type="primary"
      :plain="true"
      shape="circle"
      :hairline="true"
      text="手机号一键登录"
      :throttleTime="1000"
      customStyle="width:80%;margin:0 auto;backgroundColor:#29B7A3"
      color="#ffffff"
      @click="showAgreementCheckDialog"
    >
    </u-button>
    <u-button
      v-else
      type="primary"
      :plain="true"
      shape="circle"
      :hairline="true"
      text="手机号一键登录"
      :throttleTime="1000"
      customStyle="width:80%;margin:0 auto;backgroundColor:#29B7A3"
      color="#ffffff"
      openType="getPhoneNumber"
      @getphonenumber="onGetPhoneNumber"
    >
    </u-button>

    <view class="display-style1" style="margin-top: 20px; padding: 0 20px">
      <u-radio-group v-model="agrCheck" @change="handleAgreementCheckChange">
        <u-radio name="agrCheck" activeColor="#29B7A3"> </u-radio>
      </u-radio-group>
      <!-- <u-checkbox-group>
        <u-checkbox v-model="agrCheck" shape="circle" size="44" iconSize="28px" activeColor="#29B7A3"
          style="margin-right: 0;">
        </u-checkbox>
      </u-checkbox-group> -->
      <p style="font-size: 14px">
        我已阅读并同意<span style="color: #2979ff" @click="toLook('xy')"
          >《用户协议》</span
        >、<span style="color: #2979ff" @click="toLook('zc')">《隐私政策》</span
        >及<span style="color: #2979ff" @click="toLook('zhzx')"
          >《账号注销协议》</span
        >
      </p>
    </view>

    <view
      style="
        position: fixed;
        bottom: 30px;
        text-align: center;
        width: 100%;
        color: #29b7a3;
        text-decoration: underline;
      "
      @click="handlePasswordLoginButtonClick"
    >
      账号密码登录
    </view>

    <u-modal
      :show="isPasswordLogin"
      title="登录"
      confirmText="登录"
      cancelText="取消"
      confirmColor="#29B7A3"
      @confirm="login"
      @cancel="isPasswordLogin = false"
      :showCancelButton="true"
    >
      <view class="slot-content">
        <view class="display-style1">
          <span>账号：</span
          ><u-input
            placeholder="请输入账号"
            border="surround"
            v-model="account"
          ></u-input>
        </view>
        <view class="display-style1" style="margin-top: 20rpx">
          <span>密码：</span
          ><u-input
            password
            placeholder="请输入密码"
            border="surround"
            v-model="password"
          ></u-input>
        </view>
      </view>
    </u-modal>
    <u-popup
      :show="showPrivacyPopup"
      :closeOnClickOverlay="false"
      :round="10"
      mode="bottom"
      @close="close"
    >
      <view style="padding: 40rpx">
        <p
          style="
            text-align: center;
            margin-bottom: 40rpx;
            font-size: 20px;
            font-weight: 700;
          "
        >
          提示
        </p>
        <text>
          在你使用服务之前，请仔细阅读<text
            @click="onClickPrivacyPopupTitle()"
            style="color: #29b7a3"
            >{{ PrivacyPopupTitle }}</text
          >。如果你同意{{ PrivacyPopupTitle }}，请点击“同意”开始使用。
        </text>
        <view class="display-style" style="margin-top: 40rpx">
          <button
            class="refuse-agree"
            @click="handleRefusePrivacyAuthorization()"
          >
            拒绝
          </button>
          <!-- <u-button type="primary" id="agree-btn" text="同意" openType="agreePrivacyAuthorization"
    				@agreeprivacyauthorization="handleAgreePrivacyAuthorization()"></u-button> -->
          <button
            id="agree-btn"
            class="btn-agree"
            open-type="agreePrivacyAuthorization"
            @agreeprivacyauthorization="handleAgreePrivacyAuthorization()"
          >
            同意
          </button>
        </view>
      </view>
    </u-popup>

    <u-toast ref="uToast"></u-toast>
  </view>
</template>

<script>
const app = getApp();
import { getLatestAuth } from '@/api/passport.js';
import { initServer } from './processingLogic/submit.js';

export default {
  data() {
    return {
      agrCheck: '',
      account: '',
      password: '',
      isPasswordLogin: false,
      loadOptions: null,
    };
  },
  onLoad(options) {
    console.log('登录', options);
    this.loadOptions = options;

    const shareObj = uni.getStorageSync('shareObj');
    if (shareObj && shareObj.Type >= 0) {
      this.sendShareDataToServe(shareObj);
    }
    this.saveWXCallBack();
  },
  created() {
    this._count = 0;
  },
  methods: {
    close() {
      this.closePrivacyPopup();
    },
    showAgreementCheckDialog() {
      uni.showModal({
        title: '温馨提示',
        content:
          '请您阅读并同意以下协议《用户协议》《隐私政策》《账号注销须知》',
        success: (res) => {
          if (res.confirm) {
            this.agrCheck = 'agrCheck';
          }
        },
      });
    },
    postLoginPorcess() {
      uni.removeStorageSync('shareObj');
      getApp().globalData.indexToPlan = true;

      // console.debug(this.loadOptions);
      if (this.loadOptions.redirect) {
        let url = decodeURIComponent(this.loadOptions.redirect);
        if (!url.startsWith('/')) {
          url = '/' + url;
        }
        console.info('登录成功，跳转', url);
        uni.reLaunch({
          url,
        });
        return;
      }

      let pages = getCurrentPages();
      if (pages.length > 1) {
        // 这里需要修改，不要操作其他页面，通过通知的方式来解耦
        const prePage = pages[pages.length - 2]; //上一个页面
        if (prePage.$vm.rest) {
          prePage.$vm.rest();
        }
        if (prePage.$vm.initData) {
          prePage.$vm.initData();
        }
        console.log('登录成功，返回上一页');
        uni.navigateBack();
      } else {
        console.log('登录成功，跳转首页');
        uni.reLaunch({
          url: '/pages/index/index',
        });
      }
    },
    async login() {
      if (!this.account || !this.password) {
        this.$refs.uToast.show({
          message: '请输入账号和密码',
          type: 'error',
          duration: 1000,
        });
        return;
      }

      const r = await getApp().loginInWithAccountAndPassword(
        this.account,
        this.password
      );
      if (r) {
        this.postLoginPorcess();
      }
    },
    handlePasswordLoginButtonClick() {
      if (!this.agrCheck) {
        this.showAgreementCheckDialog();
        return;
      }
      this.isPasswordLogin = true;
    },
    sendShareDataToServe(shareObj) {
      initServer(shareObj);
    },
    toLook(type) {
      uni.navigateTo({
        url: '/subPackIndex/user/aboutDetail?type=' + type,
      });
    },
    // 微信手机号一件登录
    async onGetPhoneNumber(e) {
      uni.showLoading({
        title: '登录中，请稍后',
      });
      const r = await getApp().loginInWithWX(e);
      uni.hideLoading();
      if (r) {
        this.postLoginPorcess();
      }
    },
    handleAgreementCheckChange(n) {
      // console.log(n, this.agrCheck);
      this._count++;
      setTimeout(() => {
        if (this._count % 2 == 0) {
          this.agrCheck = '';
        } else {
          this.agrCheck = 'agrCheck';
        }
        // console.log(this.agrCheck);
      }, 0);
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  /deep/ .u-radio__icon-wrap {
    width: 25px !important;
    height: 25px !important;
  }

  &-top {
    text-align: center;
    padding: 100rpx;
    margin: 0 auto;

    /deep/ .u-image {
      margin: 0 auto;
    }
  }
}

/deep/ .u-radio {
  margin-right: 0 !important;
}
</style>
