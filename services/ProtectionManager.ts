/**
 * 强密码正则
 */
const STRONG_PASSWORD_REGEXP =
  /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[!"#$%&'()*+,-./:;<=>?@[\]^_`{|}~])[\da-zA-Z!"#$%&'()*+,-./:;<=>?@[\]^_`{|}~]{6,12}$/;

const STRONG_PASSWORD_WARN_TEXT =
  '密码至少包含一个数字、一个小写字母、一个大写字母和一个特殊字符，并在6到12位之间';

class ProtectionManager {
  private password: string | null = null;

  /**
   * 设置密码
   * @param value 密码
   */
  setPassword(value: string) {
    console.debug(value);
    this.password = value;
  }

  /**
   * 判断密码是否弱
   * @returns 是否弱
   */
  isWeak() {
    if (this.password == null) return false;
    return !STRONG_PASSWORD_REGEXP.test(this.password);
  }

  /**
   * 重置密码
   */
  reset() {
    this.password = null;
  }

  /**
   * 密码要求提示信息
   * @returns 提示信息
   */
  get tipMessage() {
    return STRONG_PASSWORD_WARN_TEXT;
  }

  /**
   * 验证密码
   * @param password 密码
   * @returns 是否有效
   */
  validatePassword(password: string) {
    return STRONG_PASSWORD_REGEXP.test(password);
  }
}

export default new ProtectionManager();
