const log = wx.getRealtimeLogManager ? wx.getRealtimeLogManager() : null;
// 环境
import config from '@/config';
const logger = {
  debug() {
    if (log) {
      log.debug.apply(log, arguments);
    }
    console.debug.apply(console, arguments);
  },
  info() {
    if (log) {
      log.info.apply(log, arguments);
    }
    console.info.apply(console, arguments);
  },
  warn() {
    if (log) {
      log.warn.apply(log, arguments);
    }
    console.warn.apply(console, arguments);
  },
  error() {
    if (log) {
      log.error.apply(log, arguments);
    }
    console.error.apply(console, arguments);
  },
  setFilterMsg(msg) {
    // 从基础库2.7.3开始支持
    if (!log || !log.setFilterMsg) return;
    if (typeof msg !== 'string') return;
    log.setFilterMsg(msg);
  },
  addFilterMsg(msg) {
    // 从基础库2.8.1开始支持
    if (!log || !log.addFilterMsg) return;
    if (typeof msg !== 'string') return;
    log.addFilterMsg(msg);
  },
  userClickLogs(other_info) {
    uni.reportEvent("user_click", {
      "env_version": config.envVersion,
      "other_info": getLogs(other_info),
      "patient_name": getApp().globalData.userInfo.Name || '未获取到患者名称',
    })
  },
  uploadLogs(other_info) {
    uni.reportEvent("upload_logs", {
      "env_version": config.envVersion,
      "other_info": getLogs(other_info),
      "patient_name": getApp().globalData.userInfo.Name || '未获取到患者名称',
    })
  }
};

function getLogs(log) {
  if (!log) return ""
  if (typeof log === 'string') return log
  return JSON.stringify(log)
}

// $u挂载到uni对象上
uni.$log = logger;

export default logger;
