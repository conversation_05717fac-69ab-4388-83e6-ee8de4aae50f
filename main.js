import Vue from 'vue';
import uView from '@/uni_modules/uview-ui';
// 日志记录器
import logger from '@/services/logger/index.js';
// 日期格式化
import {
  dateFormat,
  formateDate
} from '@/utils/validate.js';
// 环境
import config from '@/config';
// 分享
import share from '@/mixin/share.js';
// 隐私协议
import PrivacyProtocol from '@/mixin/PrivacyProtocol.js';
// IM
import {
  IMManager
} from 'kfx-im';
import imHttpClient from '@/services/im/imHttpClient';
import store from '@/store';
import App from './App';
import './constants';
import MpLoadingPage from '@/components/mp-loadingPage/mp-loadingPage.vue';
import './fix-ios-navigate-back';

Vue.config.productionTip = false;
// 先引入 uview 并挂载，其他 js 会用到 uview 中提供的工具函数
Vue.use(uView);
// 通过以下方式修改 uView 内置配置方案
// 调用setConfig方法，方法内部会进行对象属性深度合并，可以放心嵌套配置
// 需要在Vue.use(uView)之后执行
// uni.$u.setConfig({
// 	// 修改$u.config对象的属性
// 	config: {
// 		// 修改默认单位为rpx，相当于执行 uni.$u.config.unit = 'rpx'
// 		unit: 'rpx'
// 	},
// 	// 修改$u.props对象的属性
// 	props: {
// 		// 修改radio组件的size参数的默认值，相当于执行 uni.$u.props.radio.size = 30
// 		radio: {
// 			size: 15
// 		}
// 		// 其他组件属性配置
// 		// ......
// 	}
// })
Vue.prototype.$log = logger;
Vue.prototype.$dateFormat = dateFormat;
Vue.prototype.$formateDate = formateDate;
Vue.prototype.$envVersion = config.envVersion;
Vue.prototype.$loadingMsg = '正在加载数据';
Vue.prototype.$errorInputValue = '含有非法字符';
Vue.mixin(share);
Vue.mixin(PrivacyProtocol);
Vue.component('MpLoadingPage', MpLoadingPage);

IMManager.gateway = config.apiBaseUrl;
IMManager.configHttpClient(imHttpClient);

// 把 store 对象提供给 “store” 选项，这可以把 store 的实例注入所有的子组件
const app = new Vue({
  store,
  ...App,
});
app.$mount();
console.log(app);
// console.log(process.env);
// console.log(process);
// console.log(config);
