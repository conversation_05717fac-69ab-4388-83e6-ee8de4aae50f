interface GlobalData {
  userInfo: UserInfo;
  token: string;
  openId: string;
  orgId: string;
  orgName: string;
  deptId: string;
  dynamicConfig: DynamicConfigData;
}
interface UserInfo {
  Id: string;
  Roles: string[];
  Age?: number;
  Birthday?: string;
  HeadImg: string;
  Name?: string;
  NickName: string;
  PhoneNumber: string;
  Sex?: string;
  WorkflowStatus: number;
}
interface DynamicConfigData {
  relogin: boolean;
  keywordRegexp: string;
  grayVersion: string;
  protectionEnable: boolean;
}
