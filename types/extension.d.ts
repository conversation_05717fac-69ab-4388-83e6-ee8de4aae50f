declare namespace App {
  interface AppInstance<T extends AnyObject = {}> {
    globalData?: GlobalData;
    /**
     * 获取当前登录用户id
     * @returns {string}
     */
    getAccountId(): string;
    /**
     * 判断是否已登录
     */
    isLoggedIn(): boolean;
    /**
     * 判断是否已认证
     */
    isCertified(): boolean;
    /**
     * 统一跳转到聊天页面
     * @param {string} id consultId
     */
    toChatPage(id: string): void;

    /**
     * 账号密码登录
     * @param {string} account
     * @param {string} password
     * @return {Promise<boolean>} true 成功 false 失败
     */
    loginInWithAccountAndPassword(
      account: string,
      password: string
    ): Promise<boolean>;
    /**
     * 微信手机号登录
     * @param {*} event 微信登录按键事件
     * @return {Promise<boolean>} true 成功 false 失败
     */
    loginInWithWX(event: any): Promise<boolean>;
  }
}

interface Vue {
  readonly $log: Console;
}

interface Uni {
  readonly $log: Console;
}
