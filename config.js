let {
  miniProgram: {
    appId,
    envVersion,
    version
  },
} = uni.getAccountInfoSync();

// envVersion = 'develop': //开发环境
// envVersion = 'trial'; // 体验环境
// envVersion = 'release'; // 正式环境
console.log('小程序环境版本', envVersion);

const apiBaseUrlMap = {
  develop: 'https://api-dev.kangfx.com', //开发环境
  trial: 'https://api-stg.kangfx.com', //测试环境
  release: 'https://api.kangfx.com', //生产环境
};
const loginBaseUrlMap = {
  develop: 'https://api-dev.kangfx.com/passport', //开发环境
  trial: 'https://api-stg.kangfx.com/passport', //测试环境
  release: 'https://api.kangfx.com/passport', //生产环境
};
const uploadBaseUrlMap = {
  develop: 'https://oss-dev.kangfx.com', //开发环境
  trial: 'https://oss-stg.kangfx.com', //测试环境
  release: 'https://oss.kangfx.com', //生产环境
};
// 城市三级联动
const addressBaseUrlMap = {
  develop: 'https://yapi-dev.kangfx.com', //开发环境
  trial: 'https://yapi-stg.kangfx.com', //测试环境
  release: 'https://yapi.kangfx.com', //生产环境
};

const appConfigMap = {
  // 康复行
  wx5ed2e839af97c0ca: {
    resources: 'kfx',
    orgId: '', // 默认没选机构
    orgName: '',
    client: 'com.kangfx.wx.mp.patient-kfx',
    clientId: 'fcc0859e-1c2f-409d-af8f-5811dc0a9c6b',
  },
  // 河北省中医院
  wx175bd72ffb2cb559: {
    resources: 'hbszyy',
    orgId: '264cbffb-b8a8-443a-9f58-390f2e364021',
    orgName: '河北省中医院',
    client: 'com.kangfx.wx.mp.patient-hbszyy',
    clientId: 'PatientOfWeChatHbszyyMp',
  },
  // 康复行 测试
  wx078a0b1532a3f3b7: {
    resources: 'internal',
    orgId: '', // 默认没选机构
    orgName: '',
    client: 'com.kangfx.wx.mp.patient-internal',
    clientId: 'fcc0859e-1c2f-409d-af8f-5811dc0a9c6b',
  },
  // 康易行互联网医院
  wxa4a449ac8f266f01: {
    resources: 'kyx',
    orgId: '', // 默认没选机构
    orgName: '',
    client: 'com.kangfx.wx.mp.patient-kyx',
    clientId: 'PatientOfWeChatKyxMp',
  },
};
//webSocket
const websocketUrlMap = {
  develop: 'https://message-ws-dev.kangfx.com/messagebroker?client=' +
    appConfigMap[appId].clientId, //开发环境
  trial: 'https://message-ws-stg.kangfx.com/messagebroker?client=' +
    appConfigMap[appId].clientId, //模拟测试
  release: 'https://message-ws.kangfx.com/messagebroker?client=' +
    appConfigMap[appId].clientId, //生产环境
};

const config = {
  envVersion,
  hwHealthKitAppId: '111808005',
  ...appConfigMap[appId],
  apiBaseUrl: apiBaseUrlMap[envVersion],
  loginBaseUrl: loginBaseUrlMap[envVersion],
  uploadBaseUrl: uploadBaseUrlMap[envVersion],
  addressBaseUrl: addressBaseUrlMap[envVersion],
  websocketUrl: websocketUrlMap[envVersion],
  /**
   * 是否开启视频通话
   */
  videoCallEnable: true,
};

Object.defineProperty(config, 'envVersion', {
  get() {
    return envVersion;
  },
  set(v) {
    console.log('重新设置 envVersion', v);
    envVersion = v;
  },
});
Object.defineProperty(config, 'version', {
  get() {
    return version || envVersion;
  },
});
Object.defineProperty(config, 'apiBaseUrl', {
  get() {
    return apiBaseUrlMap[envVersion];
  },
});
Object.defineProperty(config, 'loginBaseUrl', {
  get() {
    return loginBaseUrlMap[envVersion];
  },
});
Object.defineProperty(config, 'uploadBaseUrl', {
  get() {
    return uploadBaseUrlMap[envVersion];
  },
});
Object.defineProperty(config, 'addressBaseUrl', {
  get() {
    return addressBaseUrlMap[envVersion];
  },
});
Object.defineProperty(config, 'websocketUrl', {
  get() {
    return websocketUrlMap[envVersion];
  },
});

export default config;
