import request from '@/libs/request';
const service = '/consult';

// -------------------------- prescriptionPath --------------------------
const prescriptionPath = service + '/api/prescription';

// 查看报告
export function getSchemeReportByPrescriptionId(parmas) {
  return request.get(
    `${prescriptionPath}/getSchemeReportByPrescriptionId`,
    parmas
  );
}

// 获取商品详情
export function getDetail(data) {
  return request.get(
    `${prescriptionPath}/GetPrescriptionAssistApplyByPrescriptionId`,
    data
  );
}
// 添加辅具申请
export function addPrescritionAssistApply(data) {
  return request.post(`${prescriptionPath}/addPrescritionAssistApply`, data);
}

// 患者查看处方
export function setPatCheckTime(data) {
  return request.post(`${prescriptionPath}/setPatCheckTime`, data);
}

// 快速开方处方
export function quickPrescription(par) {
  return request.post(`${prescriptionPath}/quickPrescription`, par);
}

// 快速开方检查是否存在相同类型的数据
export function checkExistPre(par) {
  return request.post(`${prescriptionPath}/CheckExistPre`, par);
}

// 获取我的列表
export function getPrescriptionList(parms) {
  return request.get(`${prescriptionPath}/GetPrescriptions`, parms, {
    showLoading: false,
  });
}

// 获取订单详情
export function getPrescriptionInfo(data) {
  return request.get(`${prescriptionPath}/GetPrescriptionInfo`, data);
}
//获取处方支付订单信息
export function getTreatOrderInfo(id) {
  return request.get(
    `${prescriptionPath}/GetTreatOrderInfo?prescriptionId=${id}`
  );
}

// 获取收银台里面的某些信息
export function getToExecute(data) {
  return request.post(`${prescriptionPath}/ToExecute`, data);
}
// 判断患者是否有未执行的处方
export function implementPrescription() {
  return request.get(`${prescriptionPath}/GetMyUnExecutePrescription`, '', {
    showLoading: false,
  });
}

// 获取治疗订单列表
export function getTreatOrders(prams) {
  return request.get(`${prescriptionPath}/GetTreatOrders`, prams, {
    showLoading: false,
  });
}

// 患者设备退还的列表
export function deviceBack(parms) {
  return request.get(`${prescriptionPath}/GetDevices`, parms);
}

/**
 * 获取回院提醒的详情信息
 */
export function queryRxTempBackRemind(par) {
  return request.post(`${prescriptionPath}/QueryRxTempBackRemind`, par);
}
/**
 * 生成回院提醒
 */
export function backRemindQrCode(par) {
  return request.post(`${prescriptionPath}/BackRemindQrCode`, par);
}

/**
 * 获取患者的回院提醒列表
 */
export function queryBackRemind(par) {
  return request.post(`${prescriptionPath}/QueryBackRemind`, par);
}
/**
 * 修改回院提醒的状态
 */
export function updateBackRemind(par) {
  return request.post(`${prescriptionPath}/UpdateBackRemind`, par);
}

/**
 * 检查患者是否有未填写手术时间的术后提醒
 */
export function checkUserHaveStartDate(parms) {
  return request.get(`${prescriptionPath}/CheckUserHaveStartDate`, parms);
}
/**
 * 批量更新患者未填写手术时间的术后提醒
 */
export function fillUserStartDate(par) {
  return request.post(`${prescriptionPath}/FillUserStartDate`, par);
}
/**
 * 设置宣教已读
 */
export function setRead(par) {
  return request.post(`${prescriptionPath}/SetRead`, par);
}
/**
 * 获取报告默认查询时间
 */
export function getPrescriptionDefaultReportDate(par) {
  return request.post(
    `${prescriptionPath}/GetPrescriptionDefaultReportDate`,
    par
  );
}
/**
 * 添加方案申请
 */
export function insertPreApplyRecord(par) {
  return request.post(`${prescriptionPath}/InsertPreApplyRecord`, par);
}
/**
 * 获取ai发送的训练宣教
 */
export function getRecoveryMission(data) {
  return request.post(`${prescriptionPath}/GetRecoveryMission`, data);
}

// 获取已执行的方案
export function getPatPrescription(data) {
  return request.get(`${prescriptionPath}/GetPatPrescription`, data);
}

// -------------------------- consultPath --------------------------
const consultPath = service + '/api/consult';

// 判断用户是否第一次问诊
export function checkUserFirstConsult(par) {
  return request.get(`${consultPath}/checkUserFirstConsult`, par);
}

// 获取脊柱推荐医生
export function getConsortiumUserList(par) {
  return request.post(`${consultPath}/getConsortiumUserList`, par);
}

// 将疾病标签作为病情描述更新到该 方案对应的 问诊中
export function updateConsultRecord(par) {
  return request.post(`${consultPath}/updateConsultRecord`, par);
}

// 将上传的图片作为病情描述更新到该 方案对应的 问诊中
export function updateConsultReport(par) {
  return request.post(`${consultPath}/updateConsultReport`, par);
}

// 退还设备
export function deviceBackInfo(data) {
  return request.post(`${consultPath}/returnbyorderdetail`, data);
}

// 获取关注的医生
export function getMyFollows(par) {
  return request.get(
    `${consultPath}/GetMyFollows?organizationId=${par.orgId}&userId=${par.userId}`
  );
}
// 获取找医生列表
export function findDocList(par) {
  return request.get(`${consultPath}/GetDocs`, par);
}
// 判断是否有问诊的数据
export function isOnData(data) {
  return request.get(`${consultPath}/IsConsult`, data);
}
// 获取某个医生的信息
export function getDocInfoMation(parms) {
  return request.get(`${consultPath}/GetDoctorByUserId`, parms);
}
// 获取是否有需要的评价
export function GetEvaTip(id) {
  return request.get(`${consultPath}/EvaTip?userId=` + id);
}
// 获取订单详情
export function getMyConsults(parms) {
  return request.get(`${consultPath}/GetMyConsults`, parms);
}
// 获取问诊订单列表
export function getInquiryList(parms) {
  return request.get(`${consultPath}/GetConsultByPat`, parms, {
    showLoading: false,
  });
}
// 获取问诊订单详情数据
export function getInquiryDatil(id) {
  return request.get(`${consultPath}/GetConsultInfo?id=${id}`);
}
// 取消订单
export function cancel(type, data) {
  return request.post(`${consultPath}/${type}`, data); //cancelpayorder  cancelorder
}

// 生成订单信息
export function getPayOrderInfo(data) {
  return request.post(`${consultPath}/LaunchConsult`, data);
}

/**
 * 病历档案
 *
 * @param {String} consultId 问诊Id
 */
export function getConsultRecordInfo(consultId) {
  let path = `${consultPath}/GetConsultRecordInfo`;
  let params = {
    id: consultId,
  };
  let r = request.get(path, params);
  return r;
}
// 获取首页宣教信息列表(重构)
export function getFirstRecoveryMission(data) {
  return request.post(`${consultPath}/GetFirstRecoveryMission`, data);
}

// -------------------------- followUpPath --------------------------
const followUpPath = service + '/api/FollowUp';

/**
 * 获取随访任务明细列表
 *
 * @param {Int} state 明细状态（0:未操作 1:已操作 2:已过期）
 * @param {String} orgId 机构id
 * @param {String} patientId 患者id
 */
export function getFollowUpPlanDetailList(
  state,
  orgId,
  patientId,
  pageIndex,
  pageSize
) {
  let path = `${followUpPath}/GetFollowUpPlanDetailPage`;
  var data = {
    PageIndex: pageIndex,
    PageSize: pageSize,
  };
  if (state) {
    data.State = state;
  }
  if (orgId) {
    data.OrgId = orgId;
  }
  if (patientId) {
    data.PatId = patientId;
  }
  data.PlanType = [1, 2];
  let r = request.post(path, data);
  return r;
}

// 随访是否查看宣教
export function operaFollowUpPlanDetail(par) {
  return request.post(`${followUpPath}/operaFollowUpPlanDetail`, par);
}
/**
 * 获取常用方案数据
 */
export function getRxTemplateById(id) {
  return request.get(`${prescriptionPath}/GetRxTemplateById?templateId=${id}`);
}
// -------------------------- PreVisit --------------------------
const PreVisitPath = service + '/api/PreVisit';
// 添加聊天记录
export function addPreConsultMessageRecord(data) {
  return request.post(`${PreVisitPath}/AddPreConsultMessageRecord`, data);
}
// 确定聊天数据
export function submitPreConsult(data) {
  return request.post(`${PreVisitPath}/SubmitPreConsult`, data);
}
// 诊后报道码 或 预问诊码 马上调用
export function setPreVisitRecord(data) {
  return request.post(`${PreVisitPath}/SetPreVisitRecord`, data);
}
/**
 * 按预问诊Id 设置预问诊状态
 */
export function updatePreVisitRecordState(data) {
  return request.post(`${PreVisitPath}/UpdatePreVisitRecordState`, data);
}
