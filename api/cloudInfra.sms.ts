import request from '@/libs/request/index';

const smsPath = '/cloudInfra/sms';

/// Register:注册
/// Signin:登录
/// UpdatePwd:修改密码
/// UpdatePhone:更改手机号
/// CAPasswordEdit：重置 CA 证书密码
type TemplateType =
  | 'Signin'
  | 'Register'
  | 'UpdatePwd'
  | 'UpdatePhone'
  | 'CAPasswordEdit';

/**
 * 获取验证码
 * @param params 参数
 * @param params.phoneNumber 手机号
 * @param params.templateType 模板类型
 * @returns sessionId
 */
export const sendCaptcha = (params: {
  phoneNumber: string;
  templateType: TemplateType;
}) => {
  return request.post(`${smsPath}/sendCaptcha`, {
    PhoneNumber: params.phoneNumber,
    TemplateType: params.templateType,
  });
};

/**
 * 验证验证码
 * @param params 参数
 * @param params.phoneNumber 手机号
 * @param params.captcha 验证码
 * @param params.sessionId 验证码 ID
 */
export const validCaptcha = async (params: {
  phoneNumber: string;
  captcha: string;
  sessionId: string;
}) => {
  const path = `${smsPath}/validCaptcha`;

  return await request.post(path, {
    PhoneNumber: params.phoneNumber,
    Captcha: params.captcha,
    SessionId: params.sessionId,
  });
};
