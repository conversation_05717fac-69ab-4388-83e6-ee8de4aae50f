<template>
  <view class="container">
    <block>
      <view class="container-top">
        <view class="container-top-item">
          <text class="container-top-item-left">姓名：</text>
          <text class="container-top-item-right">{{ showInfo.UserName }}</text>
        </view>
        <view class="container-top-item">
          <text class="container-top-item-left">性别：</text>
          <text class="container-top-item-right">{{ showInfo.Sex }}</text>
        </view>
        <view class="container-top-item" style="margin-top: 24rpx">
          <text class="container-top-item-left">年龄：</text>
          <text class="container-top-item-right">{{ showInfo.Age }}</text>
        </view>
        <view class="container-top-item" style="margin-top: 24rpx">
          <text class="container-top-item-left">训练时间：</text>
          <text class="container-top-item-right">{{
            showInfo.CreatedTime
          }}</text>
        </view>
      </view>
      <u-divider />
    </block>
    <!-- 图表 -->
    <view class="container-charts">
      <qiun-data-charts
        type="line"
        :ontouch="true"
        :onzoom="true"
        :tapLegend="true"
        :canvas2d="true"
        :opts="opts"
        :chartData="chartsData"
      />
    </view>
    <block>
      <view class="container-top-item1">
        <view class="container-top-item-left"> 描述： </view>
        <text class="container-top-item-right">{{ describe }}</text>
      </view>
      <u-divider />
      <view class="container-top-item1">
        <view class="container-top-item-left"> 结论： </view>
        <text class="container-top-item-right" style="color: #29b7a3">{{
          conclusion
        }}</text>
      </view>
      <u-divider />
    </block>
    <view class="flex-between-center" style="margin-top: 24rpx">
      <view class="container-top-item">
        <text class="container-top-item-left">医生：</text>
        <text class="container-top-item-right">{{
          showInfo.PreCreatorName
        }}</text>
      </view>
      <view class="container-top-item">
        <text class="container-top-item-left">报告时间：</text>
        <text class="container-top-item-right">{{ showInfo.CreatedTime }}</text>
      </view>
    </view>
  </view>
</template>

<script>
const dayjs = require('dayjs');
import { queryJCaiTechReport } from '@/api/supplier.js';
import { JC } from '@/constants';

const app = getApp();
export default {
  data() {
    return {
      actionExecuteId: '',
      showInfo: {},
      describe: '',
      conclusion: '',
      query: {
        UserId: app.globalData.userInfo.Id,
        ProgramId: null, //方案ID
        ActionExecuteId: null, //打卡记录ID
        ActionId: null, //方案明细ID
        ContentId: null, //动作基础数据ID
        BeginDate: null,
        EndDate: null,
        ActionType: null,
        PageIndex: 1,
        PageSize: 1000,
      },
      chartsData: {},
      list: [],
      opts: {
        dataLabel: false,
        color: ['#1890FF'],
        padding: [10, 0, 0, 0],
        enableScroll: true,
        legend: {},
        xAxis: {
          disableGrid: true,
          scrollShow: false,
          itemCount: 8,
          scrollAlign: 'right',
          // rotateLabel: true,
          // rotateAngle: 45
        },
        yAxis: {
          gridType: 'dash',
          dashLength: 2,
          data: [
            {
              position: 'left',
              tofix: 2,
            },
          ],
        },
        extra: {
          line: {
            type: 'curve',
            width: 2,
            activeType: 'hollow',
          },
        },
        background: 'rgb(29, 157, 219)',
      },
    };
  },
  methods: {
    async onGetData() {
      const res = await queryJCaiTechReport(this.query);
      if (res.Type === 200) {
        if (!res.Data.Data.length) {
          uni.showModal({
            title: '温馨提示',
            content: '暂无数据',
            showCancel: false,
            success: () => {
              wx.navigateBack();
            },
          });
          return;
        }
        res.Data.Data.forEach((item) => {
          item.CreatedTime = item.CreatedTime
            ? this.$dateFormat(item.CreatedTime, 'YYYY-MM-DD')
            : null;
        });
        this.showInfo = res.Data.Data.filter(
          (s) => s.ActionExecuteId === this.actionExecuteId
        )[0];
        this.list = res.Data.Data.reverse();
        this.onGetCharts();
      } else {
        uni.showModal({
          title: '温馨提示',
          content: res.Content,
          showCancel: false,
        });
      }
    },
    onGetCharts() {
      // 获取当前的  睛彩是那种类型的
      const actionType = this.query.ActionType;
      // 这种类型的精彩需要显示什么数据
      const item = JC.filter((s) => s.Type === actionType)[0];
      const showData = item.ReportValue;
      // 显示描述和结论
      this.onShowReportDesAndConclusion(showData);
      // 获取 x 轴数据
      const x_line = this.list.map((item) =>
        item.CreatedTime ? this.$dateFormat(item.CreatedTime, 'MM.DD') : null
      );
      const series = [];
      if (actionType === 4) {
        this.opts.yAxis.data.push({
          position: 'right',
          tofix: 2,
          title: '高度比例',
        });
        this.opts.yAxis.data[0].title = '角度';
        this.opts.yAxis.showTitle = true;
      }
      showData.forEach((v) => {
        let obj = {
          name: v.Name,
          data: this.list.map((item) =>
            item[v.Value] ? item[v.Value].toFixed(0) : item[v.Value]
          ),
        };
        if (actionType === 4) {
          // 如果是桥式运动（双桥）/仰卧位臀桥运动训练 需要绘制双 y 轴数据
          if (v.Value === 'KneeAngleMax') {
            obj.index = 0;
          } else {
            obj.index = 1;
          }
        }
        series.push(obj);
      });
      this.chartsData = {
        categories: x_line,
        series: series,
      };
    },
    onShowReportDesAndConclusion(list) {
      const keyIndicators = list.filter((s) => s.Type === 'KeyIndicators');
      const compensatoryData = list.filter(
        (s) => s.Type === 'CompensatoryData'
      );
      let describe = '';
      let conclusion = '';
      if (keyIndicators.length) {
        keyIndicators.forEach((s, index) => {
          if (s.StandardValue) {
            describe += `${s.Name}标准值：${s.StandardValue}，${
              index === 0 ? '本次训练' : ''
            }${s.Name}${s.ValueUnit}为：${Math.abs(
              this.showInfo[s.Value].toFixed(0)
            )}	°；`;
          }
          const conclusionList = s.Conclusion;
          const value = Math.abs(this.showInfo[s.Value].toFixed(0)) * 1;
          const conclusionIndex = conclusionList.findIndex(
            (s) => s.min <= value && s.max >= value
          );
          if (conclusionIndex > -1) {
            conclusion += `${conclusionList[conclusionIndex].content}；`;
          }
        });
      }
      if (compensatoryData.length) {
        compensatoryData.forEach((s, index) => {
          describe += `${index > 0 ? '；' : ''}${
            s.Name + '代偿' + s.ValueUnit
          }为：${this.showInfo[s.Value].toFixed(0)}°`;
          const conclusionList = s.Conclusion;
          const value = Math.abs(this.showInfo[s.Value].toFixed(0)) * 1;
          const conclusionIndex = conclusionList.findIndex(
            (s) => s.min <= value && s.max >= value
          );
          if (conclusionIndex > -1) {
            conclusion += `${conclusionList[conclusionIndex].content}；`;
          }
        });
      }
      this.describe = describe;
      this.conclusion = conclusion;
    },
  },
  onLoad(option) {
    this.query.ActionType = option.actionType * 1;
    this.query.EndDate = option.endDate;
    this.actionExecuteId = option.actionExecuteId;
    this.onGetData();
  },
};
</script>

<style lang="scss" scoped>
/deep/ .u-divider {
  margin-top: 24rpx !important;
  margin-bottom: 0 !important;
}

.container {
  padding: 36rpx 32rpx;
  background: white;

  &-charts {
    width: 100%;
    height: 400rpx;
    // margin-top: 40rpx;
  }

  &-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    width: 100%;

    &-item1 {
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;
      margin-top: 40rpx;
    }

    &-item {
      width: 50%;
      text-align: left;

      &-left {
        font-weight: 600;
        font-size: 28rpx;
        color: #333333;
        line-height: 40rpx;
      }

      &-right {
        font-weight: 400;
        font-size: 28rpx;
        color: #666666;
        line-height: 40rpx;
        margin-left: 12rpx;
        flex: 1;
      }
    }
  }
}
</style>
