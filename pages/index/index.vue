<template>
  <view class="container">
    <!-- 测试按键 -->
    <!-- 	<view style="position: fixed;top: 200px;left:10px;z-index: 1; ">
			<u-button type="warning" @click="handleTest" text="vertical"></u-button>
			<u-button type="warning" @click="handleTest1" text="landscape"></u-button>
		</view> -->
    <!-- 悬浮再首页的续方按钮 -->
    <view
      class="container-prescription"
      @click="showContinuedPrescriptionModal = true"
      v-if="showContinuedPrescription"
    >
      延续治疗
    </view>
    <u-transition :show="showTransition" mode="fade">
      <view
        @click="handleLoginclick"
        style="padding: 0 18px"
        class="sty-userInfo"
        :style="'height:' + (topHeigth + buttonHeight + 10) + 'px'"
      >
        <view
          class="sty-userInfo-st"
          :style="'height:' + (buttonHeight + 20) + 'px'"
        >
          <u-avatar :src="userInfo.HeadImg" size="40"></u-avatar>
          <p style="font-size: 14px; margin-left: 10px; color: black">
            {{ userInfo.Name || userInfo.NickName || '请点击登录' }}
            <span v-if="config.envVersion == 'trial'">(体验版)</span>
          </p>
        </view>
      </view>
    </u-transition>
    <scroll-view
      style="height: 100%"
      scroll-y="true"
      @scroll="pageScrooll($event)"
    >
      <!-- 上方背景颜色 -->
      <view class="cl-bg">
        <view class="userInfo" @click="handleLoginclick">
          <u-avatar :src="userInfo.HeadImg" size="40"></u-avatar>
          <p style="font-size: 14px; margin-left: 10px; color: #ffffff">
            {{ userInfo.Name || userInfo.NickName || '请点击登录' }}
            <span v-if="config.envVersion == 'trial'">(体验版)</span>
          </p>
        </view>
        <view class="icon-all">
          <u-icon
            name="scan"
            color="#ffffff"
            size="28"
            @click="scanInfo"
            customStyle="margin-right: 6px;"
          >
          </u-icon>
          <u-icon
            name="bell"
            color="#ffffff"
            size="28"
            customStyle="margin-right: 6px;"
            @click="lookInfo"
          >
          </u-icon>
          <u-badge :isDot="true" type="success" v-if="showbadge"></u-badge>
          <u-icon
            name="server-fill"
            color="#ffffff"
            size="28"
            customStyle="margin-right: 6px;"
            @click="toUs"
          >
          </u-icon>
          <u-icon
            v-if="
              orgDefaultConfig.SwitchOrganizationIcon.SwitchOrganizationIcon
            "
            name="/static/images/<EMAIL>"
            color="#ffffff"
            size="28"
            @click="chooseOrgIdList"
          >
          </u-icon>
        </view>
      </view>

      <!-- 中间的大框 -->
      <view class="all-box">
        <!-- 机构信息模块 -->
        <view
          class="wenz-box1 all-box-paddingStyle"
          v-if="orgInfo.HeadImg && !orgInfo.Carousel.length"
          @click="handleOrgClick"
        >
          <u--image
            shape="circle"
            :showLoading="true"
            :src="orgInfo.HeadImg"
            width="90px"
            height="90px"
            customStyle="margin-left:10px"
          ></u--image>
          <view class="orgDes">
            <p
              style="
                font-size: 20px;
                font-weight: 700;
                padding-right: 10px;
                margin-bottom: 6px;
              "
            >
              {{ orgInfo.Name }}
            </p>
            <p
              style="
                font-size: 12px;
                padding-right: 10px;
                color: gray;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 3;
                overflow: hidden;
                text-align: center;
              "
            >
              {{ orgInfo.Address || '暂无地址' }}
            </p>
          </view>
        </view>

        <view id="swiperWidth" class="all-box-paddingStyle">
          <u-swiper
            bgColor="none"
            keyName="ImgUrl"
            :list="orgInfo.Carousel"
            imgMode="heightFix"
            :height="swiperHeight"
            @click="clickSwiper($event)"
            v-if="orgInfo.HeadImg && orgInfo.Carousel.length > 0"
            :radius="4"
          >
          </u-swiper>
        </view>

        <view
          class="wenz-box1 all-box-paddingStyle"
          @click="chooseOrgIdList"
          v-if="!orgInfo.HeadImg"
        >
          <image
            src="../../static/images/hos.png"
            mode="widthFix"
            style="width: 80px; height: 80px; margin-left: 6px"
          >
          </image>
          <view class="wenz-box1-right">
            <p style="font-size: 18px; font-weight: 600">找医院</p>
            <p style="font-size: 16px; margin-top: 6px; color: #666666">
              寻找附近的好医院
            </p>
          </view>
        </view>

        <!-- 随访任务 -->
        <view
          class="title-left all-box-paddingStyle"
          v-if="GetPatNotOpera && GetPatNotOpera.TotalCount"
        >
          <p>随访任务({{ GetPatNotOpera.TotalCount }})</p>
          <p style="font-size: 14px; color: #29b7a3" @click="onSeeMoreFollowUp">
            更多
          </p>
        </view>
        <!-- 随访任务列表 -->
        <u-list
          customStyle="margin-top:10px"
          v-if="GetPatNotOpera && GetPatNotOpera.TotalCount"
        >
          <u-cell
            :border="false"
            customStyle="padding: 0 24rpx"
            @click="onToFinData"
          >
            <div
              style="
                display: flex;
                justify-items: flex-start;
                align-items: center;
              "
              slot="title"
            >
              <u-image
                :src="GetPatNotOpera.info.TypeUrl"
                width="20px"
                height="20px"
              ></u-image>
              <span
                style="margin-left: 2rpx; font-weight: 600; color: #333333"
                >{{ GetPatNotOpera.info.TypeName }}</span
              >
            </div>
            <p slot="label" style="margin-top: 8rpx">
              {{ GetPatNotOpera.info.RelatedName }}
            </p>
            <p slot="value" style="text-align: right; color: #29b7a3">
              {{
                GetPatNotOpera.info.TypeName === '宣教' ? '去查看' : '去填写'
              }}
              >
            </p>
          </u-cell>
        </u-list>

        <swiper
          class="swiper"
          circular
          style="height: 210px"
          :autoplay="true"
          v-show="inquiryStatusListLength > 0"
          :current="swiperCurrent"
        >
          <swiper-item v-for="(item, index) in inquiryStatusList" :key="index">
            <div v-show="item.ShowState == 'plan'" id="swiperItem">
              <view class="title-left all-box-paddingStyle">
                <p>今日康复训练</p>
              </view>

              <!-- 康复计划的框 -->
              <view
                class="plan-box all-box-paddingStyle"
                @click="toPlan"
                style="position: relative"
              >
                <!-- 环形 -->
                <view class="charts-box">
                  <Circle
                    :percent="todayTarget.data"
                    circleWidth="8"
                    circleColor="#29B7A3"
                    size="84"
                    direction="-90"
                  >
                    <view
                      style="
                        display: flex;
                        flex-direction: column;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      <text style="font-size: 10px; color: #c2c2c2"
                        >今日目标</text
                      >
                      <text style="font-size: 25px; color: #29b7a3">{{
                        todayTarget.label
                      }}</text>
                    </view>
                  </Circle>
                </view>
                <!-- 下方的天数和进度 -->
                <view class="day-pro">
                  <view class="day-pro-box">
                    <p style="color: #999999">剩余天数</p>
                    <p class="fontColor">
                      {{ trainingInfo.RemainingLife || 0 }}天
                    </p>
                  </view>
                  <view class="day-pro-box">
                    <p style="color: #999999">完成进度</p>
                    <p class="fontColor">
                      {{ Math.ceil(trainingInfo.FinishRate) || 0 }}%
                    </p>
                  </view>
                </view>
                <view class="card-button" @click.stop="onContactDoctor">
                  <text>咨询指导</text>
                  <u-badge
                    numberType="overflow"
                    :absolute="true"
                    :offset="[-5, -10]"
                    max="99"
                    :value="unReadCounts"
                  >
                  </u-badge>
                </view>
              </view>
            </div>

            <div v-if="item.ShowState == 'Inquiry'">
              <view class="title-left all-box-paddingStyle">
                <p>在线咨询</p>
              </view>
              <div
                class="all-box-paddingStyle inquiryStyleCss"
                @click="toIm(item)"
                style="margin-top: 10px; position: relative"
              >
                <u-cell
                  customStyle="height: 165px;padding:20px"
                  :border="false"
                >
                  <u-image
                    slot="icon"
                    :src="item.DocUserHeadImg"
                    width="100px"
                    height="100px"
                    :radius="8"
                  >
                  </u-image>
                  <div
                    slot="title"
                    style="margin-left: 16px; margin-bottom: 12px"
                  >
                    <p
                      style="
                        font-size: 18px;
                        font-weight: 600;
                        margin-bottom: 12px;
                      "
                    >
                      {{ item.DocUserName }}
                    </p>
                    <p style="font-size: 16px; color: #999999">
                      <span style="margin-bottom: 12px; margin-right: 10px">{{
                        item.OrganizationName
                      }}</span>
                      <span>{{ item.DepartmentName }}</span>
                    </p>
                  </div>
                  <p
                    slot="label"
                    style="font-size: 16px; color: #999999; margin-left: 16px"
                  >
                    {{ item.showDate }}
                  </p>
                </u-cell>
                <view class="card-button">
                  <text>咨询指导</text>
                  <u-badge
                    numberType="overflow"
                    :absolute="true"
                    :offset="[-5, -10]"
                    max="99"
                    :value="item.unreadCount || ''"
                  >
                  </u-badge>
                </view>
              </div>
            </div>
          </swiper-item>
        </swiper>

        <!-- 脊柱健康陪伴计划功能 -->
        <view
          class="all-box-spinal"
          v-if="handleShowSpinal()"
          @click="handleToSpinal"
        >
          <view class="all-box-spinal-btn" @click.stop="handleShareSpinal">
            去分享
          </view>
        </view>

        <!-- 老年人风险预警 -->
        <view
          class="all-box-riskWarning"
          v-if="orgDefaultConfig.OldPeopleRiskWarn.OldPeopleRiskWarn"
          @click="handleRiskWarningClick"
        >
          <image
            src="https://kfxoss-biz.oss-cn-hangzhou.aliyuncs.com/static/frontend/mp-patient/risk-warning.png"
            style="width: 152rpx; height: 152rpx"
          ></image>
          <view class="all-box-riskWarning-right">
            <text class="all-box-riskWarning-right-title">老年人风险预警</text>
            <text class="all-box-riskWarning-right-described"
              >精准预测预警老年人常见运动、认知功能障碍</text
            >
          </view>
        </view>

        <view class="all-box-paddingStyle all-box-docbox">
          <!-- 科室 -->
          <view class="dept-subsection" v-if="orgInfo && orgInfo.Id">
            <MpTabs
              :list="deptTabs"
              @click="ondeptTabsChange"
              keyName="Name"
              :current="deptTabsCurrent"
            />
          </view>

          <!-- 在线问诊 -->
          <view
            class="title-left all-box-paddingStyle"
            v-if="docList.length > 0"
          >
            <p>医生</p>
            <p style="font-size: 14px; color: #29b7a3" @click="toSeeDoc('doc')">
              更多
            </p>
          </view>

          <!-- 部分医生列表模块 -->
          <u-scroll-list :indicator="false" v-if="docList.length > 0">
            <view
              v-for="(item, index) in docList"
              :key="index"
              style="margin-top: 10px"
            >
              <view class="doc-info-each" @click="toWenZhen(item)">
                <u--image
                  :radius="10"
                  errorIcon
                  :showLoading="true"
                  :src="item.Doctor.HeadImg"
                  width="80px"
                  height="80px"
                  customStyle="margin-left:18rpx"
                ></u--image>
                <view
                  class="doc-info-each-right"
                  style="overflow: hidden; padding-right: 4rpx"
                >
                  <p style="font-size: 14px; color: #fb9a54">
                    {{ item.Doctor.DepartmentName || '' }}
                  </p>
                  <p style="font-size: 12px; color: gray">
                    <span
                      style="
                        font-size: 16px;
                        font-weight: 700;
                        color: black;
                        margin-right: 6rpx;
                      "
                      >{{ item.Doctor.Name }}</span
                    >
                    {{ item.Doctor.WorkerTitle || '' }}
                  </p>
                  <p
                    style="
                      font-size: 12px;
                      color: #666666;
                      overflow: hidden;
                      text-overflow: ellipsis;
                      white-space: nowrap;
                    "
                  >
                    擅长：{{ item.Doctor.Skilled || '' }}
                  </p>
                </view>
                <view
                  class="right-button"
                  :style="
                    item.IsEnable ? '' : 'background-color:#C7C7C7;color:black'
                  "
                  @click.stop="onOnlineMedicalInquiries(item)"
                  >咨询</view
                >
              </view>
            </view>
          </u-scroll-list>
          <view
            class="title-left all-box-paddingStyle"
            v-if="therapistList.length > 0"
          >
            <p>治疗师</p>
            <p
              style="font-size: 14px; color: #29b7a3"
              @click="toSeeDoc('therapist')"
            >
              更多
            </p>
          </view>
          <!-- 部分治疗师列表模块 -->
          <u-scroll-list :indicator="false" v-if="therapistList.length > 0">
            <view
              v-for="(item, index) in therapistList"
              :key="index"
              style="margin-top: 10px"
            >
              <view class="doc-info-each" @click="toWenZhen(item)">
                <u--image
                  :radius="10"
                  errorIcon
                  :showLoading="true"
                  :src="item.Doctor.HeadImg"
                  width="80px"
                  height="80px"
                  customStyle="margin-left:18rpx"
                ></u--image>
                <view
                  class="doc-info-each-right"
                  style="overflow: hidden; padding-right: 4rpx"
                >
                  <p style="font-size: 14px; color: #fb9a54">
                    {{ item.Doctor.DepartmentName }}
                  </p>
                  <p style="font-size: 12px; color: gray">
                    <span
                      style="
                        font-size: 16px;
                        font-weight: 700;
                        color: black;
                        margin-right: 6rpx;
                      "
                      >{{ item.Doctor.Name }}</span
                    >
                    {{ item.Doctor.WorkerTitle || '' }}
                  </p>
                  <p
                    style="
                      font-size: 12px;
                      color: #666666;
                      overflow: hidden;
                      text-overflow: ellipsis;
                      white-space: nowrap;
                    "
                  >
                    擅长：{{ item.Doctor.Skilled || '' }}
                  </p>
                </view>
                <view
                  class="right-button"
                  :style="
                    item.IsEnable ? '' : 'background-color:#C7C7C7;color:black'
                  "
                  >咨询
                </view>
              </view>
            </view>
          </u-scroll-list>
        </view>

        <view
          class="title-left all-box-paddingStyle"
          v-if="OrganizationConsortiumInfo.List.length > 0"
        >
          <p>{{ OrganizationConsortiumInfo.Name }}</p>
          <p
            style="font-size: 14px; color: #29b7a3"
            @click="onMoreConsortiumClick"
          >
            更多
          </p>
        </view>

        <u-scroll-list
          :indicator="false"
          v-if="OrganizationConsortiumInfo.List.length > 0"
        >
          <view
            v-for="(item, index) in OrganizationConsortiumInfo.List"
            :key="index"
            style="margin-top: 10px"
          >
            <view class="org-info-each" @click="onConsortiumClick(item)">
              <image
                class="org-info-each-img"
                :src="item.OrganizationHeadImg"
              />
              <text class="org-info-each-name">{{
                item.OrganizationName
              }}</text>
            </view>
          </view>
        </u-scroll-list>

        <!-- 快捷入口 -->
        <view class="container-doc-sq display-style">
          <block v-for="(item, index) in expressEntrance" :key="index">
            <view class="container-doc-sq-each" @click="onHandleTo(item)">
              <view class="container-doc-sq-each-style display-style1">
                <u--image
                  mode="aspectFit"
                  :showLoading="true"
                  :src="item.icon"
                  width="96rpx"
                  height="96rpx"
                >
                </u--image>
                <p style="margin-left: 18rpx">{{ item.name }}</p>
              </view>
              <text
                v-if="reminderTime && item.name === '回院提醒'"
                class="container-doc-sq-each-style-time"
                >{{ reminderTime }}</text
              >
            </view>
          </block>
        </view>

        <!-- 康复宣教 -->
        <view
          class="title-left all-box-paddingStyle"
          v-if="indexList.length > 0"
        >
          <p>康复宣教</p>
          <p style="font-size: 14px; color: #29b7a3" @click="more">更多</p>
        </view>

        <!-- 宣教的列表 -->
        <u-list customStyle="margin-top:10px" v-if="indexList.length > 0">
          <u-list-item v-for="(item, index) in indexList" :key="index">
            <u-cell @click="toDetail(item)" :border="false">
              <p
                slot="title"
                class="p-style"
                style="color: #333333; margin-left: 20rpx; font-size: 32rpx"
              >
                {{ item.Title }}
              </p>
              <u--image
                radius="10"
                :lazyLoad="true"
                :showLoading="true"
                slot="icon"
                :src="item.ShowImg"
                width="70px"
                height="70px"
                customStyle="margin-left:24rpx"
              ></u--image>
            </u-cell>
          </u-list-item>
        </u-list>
      </view>
    </scroll-view>

    <u-toast ref="uToast"></u-toast>
    <u-modal
      :show="show"
      :closeOnClickOverlay="true"
      @close="cancel"
      title="温馨提示"
      content="您还没有登录,请登录之后再查看"
    >
      <button
        type="primary"
        slot="confirmButton"
        open-type="getPhoneNumber"
        @getphonenumber="getPhoneNumber"
      >
        微信一键登录
      </button>
    </u-modal>
    <u-modal
      :show="showModal2"
      title="提示"
      content="您还没有实名认证,请先实名认证"
      confirmText="去认证"
      cancelText="取消"
      confirmColor="#29B7A3"
      @confirm="confirm2"
      @cancel="showModal2 = false"
      :showCancelButton="true"
    >
    </u-modal>
    <u-modal
      :show="showContinuedPrescriptionModal"
      title="温馨提示"
      :content="ContinuedPrescriptionModal.content"
      showConfirmButton="true"
      confirmText="确定"
      @confirm="onDetectionStatus"
      showCancelButton="true"
      cancelText="取消"
      @cancel="showContinuedPrescriptionModal = false"
    >
    </u-modal>
    <u-modal
      :show="showBind"
      title="温馨提示"
      content="已绑定相同类型的设备,如需绑定新的设备,请先解绑？"
      showConfirmButton="true"
      showCancelButton="true"
      confirmText="去解绑"
      @confirm="onSure"
      @cancel="showBind = false"
    ></u-modal>
    <u-modal
      :show="showWZModal"
      title="温馨提示"
      content="您现在没有康复方案,请先发起咨询"
      showConfirmButton="true"
      confirmText="好的"
      cancelText="不用"
      showCancelButton="true"
      @confirm="showWZModalConfirm"
      @cancel="showWZModal = false"
    ></u-modal>

    <u-modal
      :show="onShowToWZ"
      title="提示"
      :buttonReverse="true"
      content="当前已有进行中的咨询,是否确认发起新的咨询？"
      confirmText="是"
      cancelText="否"
      cancelColor="#29B7A3"
      confirmColor="#606266"
      @confirm="toWZconfirm"
      @cancel="toWZcancel"
      :showCancelButton="true"
    ></u-modal>

    <u-modal
      :show="onShowToPJ"
      title="提醒"
      content="本次咨询已结束，来说说您的感受和建议吧"
      confirmText="去评价"
      cancelText="以后再说"
      confirmColor="#29B7A3"
      @confirm="toPJconfirm"
      @cancel="onShowToPJ = false"
      :showCancelButton="true"
    >
    </u-modal>
    <u-modal
      :show="showRehabilitationFinish"
      title="提醒"
      content="本次康复训练已结束，来说说您的感受和建议吧"
      confirmText="去评价"
      cancelText="以后再说"
      confirmColor="#29B7A3"
      @confirm="toSubServicesfirm"
      @cancel="showRehabilitationFinish = false"
      :showCancelButton="true"
    >
    </u-modal>
    <u-modal
      :show="showGiveMoeny"
      title="温馨提示"
      :content="PrescriptionDocName + '已为您下达治疗方案，请及时查看'"
      showConfirmButton="true"
      confirmText="去执行"
      @confirm="onSureToGiveMoeny"
      showCancelButton="true"
      cancelText="暂缓"
      @cancel="cancelPreId"
    >
    </u-modal>
    <u-modal
      :show="onShowAgrrment"
      :confirmText="confirmAgrrmentText"
      cancelText="不同意"
      @confirm="confirm1"
      @cancel="cancel1"
      :showCancelButton="true"
      :confirmColor="timeDel == 0 ? '#29B7A3' : '#606266'"
    >
      <view
        v-html="contents"
        slot="default"
        :style="'height:' + windowHeight + 'px'"
      ></view>
    </u-modal>
    <u-overlay :show="showSpinalOverlay" @click="showSpinalOverlay = false">
      <view class="spinal-overlay-warp">
        <view class="spinal-overlay-warp-container">
          <view class="spinal-overlay-warp-container-banner">
            <!-- <QrCode v-if="showSpinalOverlay" :size="130" :text="shareTextUrl" /> -->
            <ikun-qrcode
              v-if="showSpinalOverlay"
              width="260"
              height="260"
              unit="rpx"
              :data="shareTextUrl"
            />
          </view>
          <view class="spinal-overlay-warp-container-text">微信扫一扫加入</view>
          <view class="spinal-overlay-warp-container-text"
            >青少年脊柱发育健康成长陪伴计划</view
          >
          <button
            open-type="share"
            @click="handleSetShareInfo"
            class="spinal-overlay-warp-container-button"
          >
            分享给微信好友
          </button>
        </view>
      </view>
    </u-overlay>
  </view>
</template>

<script>
const app = getApp();
import config from '@/config';
import { OrganizationConsortiumGet } from '@/api/passport.js';
import { getNotice } from '@/api/other.js';
import {
  arrayNotEmpty,
  objNotEmpty,
  getUrlParams,
  dataIsValid,
  stringNotEmpty,
  getUserEnterOptions,
} from '@/utils/utils.js';
import {
  MessageClientEvent,
  SessionClientEvent,
  PrescriptionClientEvent,
} from '@/utils/eventKeys.js';
import { IMApi, IMManager } from 'kfx-im';
import { createNamespacedHelpers } from 'vuex';
import {
  checkBindingUserDevices,
  bindingUserDevices,
  checkApplyContinuePreState,
  applyContinueRx,
} from '@/api/training.js';
import { agreementTime, onGetOrgDefault } from '@/api/bff.js';
const { mapGetters, mapMutations, mapState } =
  createNamespacedHelpers('message');
import { initServer } from '@/subGauge/processingLogic/submit.js';
import {
  isOnData,
  getMyConsults,
  checkUserFirstConsult,
  getFirstRecoveryMission,
} from '@/api/consult.js';
import {
  getTrainingInfoForIndex,
  getConsultInfoForIndex,
  getConsultDocIndex,
} from '@/api/pageIndex.js';
import { getOrganizationById } from '@/api/passport.js';
import SessionState from '@/libs/util/session_state';
import MpTabs from '@/components/mp-tabs/u-tabs.vue';
import { videoCheckIn } from '../../subPackTraining/video-check-in';
import Circle from '@/components/Circle/Circle.vue';
import { getMissionImageUrl } from '../../utils/mission';
import { queryDepartments } from '../../api/passport.department';
import QrCode from '@/components/mp-qrcode/mp-qrcode.vue';
import ProtectionManager from '@/services/ProtectionManager';

export default {
  components: {
    MpTabs,
    Circle,
    QrCode,
  },
  data() {
    return {
      reminderTime: '',
      OrganizationConsortiumInfo: {
        List: [],
        Name: '',
      },
      deptTabs: [],
      deptTabsCurrent: 0,
      showRehabilitationFinish: false,
      tosubServicesId: '',
      GetPatNotOpera: {},
      consultId: '', // 记录问诊结束后的问诊iD
      onShowToPJ: false,
      windowHeight: 0,
      contents: '',
      timeDel: 0,
      TimeDel: 0,
      confirmAgrrmentText: '',
      onShowAgrrment: false,
      onShowToWZ: false,
      showWZModal: false,
      ContinuedPrescriptionModal: {
        content: '',
        prescriptionId: '',
      },
      showContinuedPrescriptionModal: false,
      showContinuedPrescription: false,
      showModal2: false,
      swiperCurrent: 0,
      querys: {
        year: 2022,
        month: 10,
        pageIndex: 1,
        pageSize: 1000,
        states: '1,2',
      },
      inquiryStatusList: [],
      swiperHeight: 150,
      showBind: false,
      showTransition: false,
      showSty: false,
      swiperArr: [],
      docList: [],
      orgInfo: {},
      userInfo: {},
      show: false,
      query: {
        PageIndex: 1,
        PageSize: 6,
        PatId: null,
        OrgId: null,
      },
      trainingInfo: {},
      indexList: [],
      todayTarget: {
        data: 0,
        label: '0/0',
      },
      topHeigth: '',
      buttonHeight: '',
      unReadCounts: 0,
      option: {
        showToDocReport: false,
      },
      showToReport: false,
      therapistList: [],
      toWZDocInfo: {},
      onceInquiry: true, // 是否第一次问诊
      PrescriptionDocName: '',

      /**
       * 是否需要网络请求会话未读数
       *
       * 初始化时候需要通过网络请求来同步消息未读数，之后医生端发送消息，
       * 未读数变化，IMManager 内部会自动缓存本 session，不需要再网络请求
       */
      needRequestTrainningUnreadCount: true,
      needRequestConsultUnreadCount: true,
      expressEntrance: [
        {
          name: '线下治疗',
          icon: '/static/images/xxzlsy.png',
          url: '/subPackIndex/community/index',
        },
        {
          name: '体征记录',
          icon: '/static/images/tzlrsy.png',
          url: '/subPhysical/index',
        },
        {
          name: '评估档案',
          icon: '/static/images/pgdasy.png',
          url: '/subGauge/gaugeArchives',
        },
        {
          name: '回院提醒',
          icon: '/static/images/hytxsy.png',
          url: '/subPackIndex/user/returningHospital',
        },
      ],
      orgDefaultConfig: {
        OldPeopleRiskWarn: {
          OldPeopleRiskWarn: false,
        },
        SwitchOrganizationIcon: {
          SwitchOrganizationIcon: true,
        },
      },
      showSpinalOverlay: false,
      shareTextUrl: '',
    };
  },
  computed: {
    ...mapState(['prescriptionId']),
    ...mapGetters(['showbadge', 'showGiveMoeny']),
    inquiryStatusListLength() {
      return this.inquiryStatusList.length;
    },
  },
  async onLoad(option) {
    console.log('onLoad', option);
    await getApp().isLaunchFinish();
    this.option = option;
    this.onGetTopRightParms();
    this.initData(option.orgId, option.orgName, option.share);
    uni.$on(MessageClientEvent.messageReceived, this.messageReceived);
    this.handleListenSessionListChanged();
    this.handleToOtherPage(option);
    this.getSysInfo();
  },
  methods: {
    checkWeakPassword() {
      console.log('检测密码强度');
      console.log(ProtectionManager.isWeak());
      console.log(getApp().globalData.dynamicConfig);
      // 2. 如果密码弱，提示去修改密码
      if (getApp().globalData.dynamicConfig && ProtectionManager.isWeak()) {
        // this.dialogTaskManager?.addTask({
        //   key: `密码弱`,
        //   task: new DialogTask(() => {
        uni.showModal({
          title: '提示',
          content: '您的密码强度较弱，建议修改密码',
          showCancel: false,
          success: () => {
            uni.reLaunch({
              url: '/subPackIndex/ChangePassword',
            });
          },
        });
        //   }),
        // });
      }
    },
    handleShowSpinal() {
      const orgId = app.globalData.orgId;
      if (!orgId) return false;
      const env = config.envVersion;
      const mapItem = {
        develop: ['bbdcbc14-290f-43f6-91d9-fd31529dbec3'],
        trial: [
          '47fcdd91-c0b3-4801-b2ec-6c92d6bbc314',
          'bbdcbc14-290f-43f6-91d9-fd31529dbec3',
        ],
        release: [
          'cdbd146e-6cff-45e5-9873-b729bed4f614',
          '53c6df17-ba27-4983-a21a-2f3b5eb8fcb4',
        ],
      };
      if (mapItem[env].some((s) => s === orgId)) return true;
      return false;
    },
    handleSetShareInfo() {
      this.isUseDefaultParams = false;
      this.share.title = '青少年脊柱发育健康成长陪伴计划';
      this.share.imageUrl =
        'https://kfxoss-biz.oss-cn-hangzhou.aliyuncs.com/static/frontend/mp-patient/index-share-image.png';
      this.share.path = `/subGauge/transfer?handler=share&path=/subReport/SpineScreening&shareUserId=${app.globalData.userInfo.Id}`;
      setTimeout(() => {
        this.isUseDefaultParams = true;
        this.share.title = '康易行小程序';
        this.share.imageUrl = '';
        this.share.path = '/pages/index/index';
      }, 500);
    },
    handleShareSpinal() {
      if (!app.globalData.userInfo.Id) {
        this.show = true;
        return;
      }
      this.shareTextUrl =
        'https://oss-biz.kangfx.com?shareUserId=' + app.globalData.userInfo.Id;
      this.showSpinalOverlay = true;
    },
    handleToSpinal() {
      if (!app.globalData.userInfo.Id) {
        app.openLoginPage();
        return;
      }
      uni.navigateTo({
        url: '/subReport/SpineScreening',
      });
    },
    handleOrgClick() {
      const obj = {
        Name: this.orgInfo.Name,
        Address: this.orgInfo.Address,
        Phone: this.orgInfo.Phone,
        Work: this.orgInfo.Work,
        Remark: this.orgInfo.Remark,
      };
      uni.navigateTo({
        url:
          '/subPackIndex/hospital/introduce?item=' +
          encodeURIComponent(JSON.stringify(obj)),
      });
    },
    /**
     * 老年人预警点击事件
     */
    handleRiskWarningClick() {
      if (!app.globalData.userInfo.Id) {
        app.openLoginPage();
        return;
      }
      uni.navigateTo({
        url: '/subRiskWarning/index',
      });
    },
    handleTest() {
      const tip =
        '家属手持手机站在患者侧方，镜头正对人体正侧面，保持竖屏状态，稳定进行拍摄，确保整个人像在手机屏幕中间';
      const image =
        'https://kfxoss-biz.oss-cn-hangzhou.aliyuncs.com/static/frontend/mp-patient/%E8%BA%AF%E5%B9%B2%E5%B1%88%E8%82%8C%E4%BC%B8%E5%B1%95.png';
      videoCheckIn({
        actionId: 'this.actionId',
        trainingProgramId: 'this.trainingPlanId',
        direction: 'vertical',
        tip,
        image,
      });
    },
    handleTest1() {
      const tip =
        '家属手持手机站在患者侧方，镜头正对人体正侧面，保持竖屏状态，稳定进行拍摄，确保整个人像在手机屏幕中间';
      const image =
        'https://kfxoss-biz.oss-cn-hangzhou.aliyuncs.com/static/frontend/mp-patient/%E8%BA%AF%E5%B9%B2%E5%B1%88%E8%82%8C%E4%BC%B8%E5%B1%95.png';
      videoCheckIn({
        actionId: 'this.actionId',
        trainingProgramId: 'this.trainingPlanId',
        direction: 'landscape',
        tip,
        image,
      });
    },
    ...mapMutations(['updatePrescriptionId']),
    /**
     * 点击切换某个医联体医院
     */
    async onConsortiumClick(item) {
      await app.changeOrgAndMark({
        orgId: item.OrganizationId,
        orgName: item.OrganizationName,
        deptId: '',
      });
      this.initData();
    },
    handleToOtherPage(option) {
      if (option.sessionType) {
        // 说明准备跳转到聊天页面
        if (option.consultId) {
          // 跳转到1v1
          uni.navigateTo({
            url: `/subPackChat/sessionChatPage?consultId=${option.consultId}`,
          });
        } else {
          // 跳转到指导群
          uni.navigateTo({
            url: `/subPackChat/sessionChatPage`,
          });
        }
      }
    },
    handleListenSessionListChanged() {
      // 会话列表变化，刷新会话未读数量
      this.sessionListChangedHandler = (sessions) => {
        // 获取今日训练会话未读数量
        this.refreshTrainingUnReadCount();

        // 咨询记录对应的未读数量
        this.refreshConsultsUnReadCount();
      };
      uni.$on(
        SessionClientEvent.sessionListChanged,
        this.sessionListChangedHandler
      );

      this.payTreatSuccessHandler = () => {
        this.$store.commit('message/updatePrescriptionId', '');
      };
      uni.$on(
        PrescriptionClientEvent.payTreatSuccess,
        this.payTreatSuccessHandler
      );
    },
    // 计算右上角胶囊参数
    onGetTopRightParms() {
      // 计算高度
      let menuButtonInfo = uni.getMenuButtonBoundingClientRect();
      this.topHeigth = menuButtonInfo.top;
      this.buttonHeight = menuButtonInfo.height;
    },
    async ondeptTabsChange(item) {
      app.globalData.deptId = item.Id;
      uni.setStorageSync('deptId', item.Id);
      const res = await this.getFinDocList();
      if (res.Type === 200) {
        this.handleGetFinDocList(res.Data);
      }
    },
    onSeeMoreFollowUp() {
      uni.navigateTo({
        url: '/subFollowUp/followUpDetailListPage',
      });
    },
    onToFinData() {
      if (this.GetPatNotOpera.info.Type !== 2) {
        uni.navigateTo({
          url: `/subFollowUp/autonomy?id=${this.GetPatNotOpera.info.RelatedId}&RelatedId=${this.GetPatNotOpera.info.ShowId}&State=${this.GetPatNotOpera.info.State}`,
        });
      } else {
        uni.navigateTo({
          url:
            '/subPropaganda/detail?id=' +
            this.GetPatNotOpera.info.RelatedId +
            '&ShowId=' +
            this.GetPatNotOpera.info.ShowId,
        });
      }
    },
    toPJconfirm() {
      this.onShowToPJ = false;
      uni.navigateTo({
        url: '/subServices/docIndex?consultId=' + this.consultId,
      });
    },
    toSubServicesfirm() {
      this.showRehabilitationFinish = false;
      uni.navigateTo({
        url: '/subServices/planIndex?Id=' + this.tosubServicesId,
      });
    },
    confirm1() {
      if (this.timeDel > 0) {
        return;
      }
      this.onShowAgrrment = false;
      this.timeDel = this.TimeDel;
      if (this.onceInquiry) {
        uni.navigateTo({
          url:
            '/subPackIndex/healthInformation?doctorId=' +
            this.toWZDocInfo.Doctor.UserId +
            '&orgId=' +
            this.toWZDocInfo.Doctor.OrganizationId,
        });
        return;
      }
      uni.navigateTo({
        url:
          '/subPrescription/InterviewDescription?doctorId=' +
          this.toWZDocInfo.Doctor.UserId,
      });
    },
    cancel1() {
      this.onShowAgrrment = false;
      this.timeDel = this.TimeDel;
    },
    toWZconfirm() {
      this.onShowToWZ = false;
      this.onShowAgrrment = true;
      if (!this.timeDel || this.timeDel == 0) {
        this.confirmAgrrmentText = '同意';
      } else {
        this.confirmAgrrmentText = '同意' + `(${this.timeDel}s)`;
        let setIntervalTime = setInterval(() => {
          this.timeDel--;
          this.confirmAgrrmentText = '同意' + `(${this.timeDel}s)`;
          if (this.timeDel == 0) {
            this.confirmAgrrmentText = '同意';
            clearInterval(setIntervalTime);
          }
        }, 1000);
      }
    },
    onMoreConsortiumClick() {
      uni.navigateTo({
        url:
          './chooseOrg?consortiumId=' +
          this.orgInfo.OrganizationConsortiums[0].ConsortiumId,
      });
    },
    toWZcancel() {
      this.onShowToWZ = false;
      uni.reLaunch({
        url: `/pages/interview/index?type=0}`,
      });
    },
    getSysInfo() {
      uni.getSystemInfo({
        success: (res) => {
          this.windowHeight = res.windowHeight * 0.75;
        },
      });
    },
    async getNot() {
      let res = await getNotice();
      this.contents = res;
    },
    async agreementTimeInfo() {
      let res = await agreementTime();
      if (res.Type == 200) {
        const itemList = res.Data.filter(
          (v) => v.Code === 'PatientReadingTime'
        );
        if (itemList.length > 0) {
          this.timeDel = itemList[0].Payload[0].Value;
          this.TimeDel = itemList[0].Payload[0].Value;
        }
      }
    },
    async onOnlineMedicalInquiries(item) {
      if (!this.checkIsLogin()) return;
      if (!this.checkIsAug()) return;
      let resData = await checkUserFirstConsult({
        userId: app.globalData.userInfo.Id,
      });
      if (resData.Type === 200) {
        this.onceInquiry = resData.Data;
      }
      this.toWZDocInfo = item;
      await this.getNot();
      await this.agreementTimeInfo();
      if (item.IsEnable) {
        let res = await isOnData();
        if (
          res.Type == 200 &&
          (res.Data.Consult || res.Data.Advisory || res.Data.Nurse)
        ) {
          this.onShowToWZ = true;
        } else if (
          res.Type == 200 &&
          !res.Data.Consult &&
          !res.Data.Advisory &&
          !res.Data.Nurse
        ) {
          this.toWZconfirm();
        }
      } else {
        this.toWenZhen(item);
      }
    },
    showWZModalConfirm() {
      this.showWZModal = false;
      this.toSeeDoc('doc');
    },
    handleLoginclick() {
      this.checkIsLogin();
    },
    confirm2() {
      this.showModal2 = false;
      uni.navigateTo({
        url: '/subPackIndex/user/userUpdate?type=smxx',
      });
    },
    // 点击咨询指导
    async onContactDoctor(e) {
      // 获取居家训练会话
      uni.showLoading({
        title: '正在打开...',
      });
      let roomId = this.trainingInfo.RoomId;
      var session;
      try {
        session = await SessionState.instance().findSession(roomId);
        uni.hideLoading();
      } catch (e) {
        uni.showToast({
          title: e,
          icon: 'none',
        });
        return;
      }

      if (objNotEmpty(session)) {
        uni.navigateTo({
          url: '/subPackChat/sessionChatPage?roomId=' + roomId,
        });
      } else {
        uni.showToast({
          title: '未获取到会话',
          icon: 'none',
        });
      }
    },
    // 点击在咨询的问诊
    toIm(item) {
      getApp().toChatPage(item.Id);
    },
    cancelPreId() {
      this.$store.commit('message/updatePrescriptionId', '');
    },
    messageReceived(data) {
      if (data.Type == 25 && data.Extras) {
        const prescriptionId = JSON.parse(data.Extras).PrescriptionId;
        // this.PrescriptionDocName = JSON.parse(data.Extras).CreatorName
        if (JSON.parse(data.Extras).Role === 'doctor') {
          // this.PrescriptionDocName += '医生'
          this.PrescriptionDocName = '医生';
        } else if (JSON.parse(data.Extras).Role === 'therapist') {
          // this.PrescriptionDocName += '治疗师'
          this.PrescriptionDocName = '治疗师';
        }
        this.$store.commit('message/updatePrescriptionId', prescriptionId);
      }
    },
    onHandleTo(item) {
      if (!this.checkIsLogin()) return;
      console.log('item', item);
      uni.navigateTo({
        url: item.url,
      });
    },
    // 点击去查看处方 准备给钱
    onSureToGiveMoeny() {
      const prescriptionId = this.$store.state.message.prescriptionId;
      this.$store.commit('message/updatePrescriptionId', '');
      uni.navigateTo({
        url: '/subPrescription/detail?id=' + prescriptionId,
      });
    },
    // 点击轮播图
    clickSwiper(e) {
      if (!this.orgInfo.Carousel[e].Url) return;
      if (this.orgInfo.Carousel[e].Url.includes('https')) {
        uni.navigateTo({
          url:
            '/subPackIndex/hospital/introduce?url=' +
            encodeURIComponent(this.orgInfo.Carousel[e].Url),
        });
      } else {
        uni.navigateTo({
          url: this.orgInfo.Carousel[e].Url,
        });
      }
    },
    toUs() {
      uni.navigateTo({
        url: '/subPackIndex/customerService',
      });
    },
    lookInfo() {
      if (!this.checkIsLogin()) return;
      uni.navigateTo({
        url: '/subPackIndex/news',
      });
    },
    // 开始测评
    toEvaluation() {
      if (!this.checkIsLogin()) return;
      if (!this.checkIsAug()) return;
      uni.navigateTo({
        url: '/subGauge/index',
      });
    },
    toDetail(item) {
      uni.navigateTo({
        url: '/subPropaganda/detail?id=' + item.ContentId,
      });
    },
    async getDeptList() {
      if (app.globalData.orgId) {
        const r = await queryDepartments({
          OrgId: app.globalData.orgId,
          IsEnabled: true,
          Pageable: false,
          SingleOne: false,
        });
        if (r.Type != 200) {
          console.warn(r.Content);
          uni.showToast(r.Content);
          return;
        }
        const data = r.Data;
        data.unshift({
          Id: '',
          Name: '全部',
        });
        this.deptTabs = data;
        const deptId = app.globalData.deptId;
        const index = data.findIndex((v) => v.Id === deptId);
        this.deptTabsCurrent = index;
      }
    },
    //获取医生列表
    getFinDocList() {
      const data = {
        OrganizationId: app.globalData.orgId,
        pageindex: 1,
        pagesize: 6,
      };
      if (app.globalData.deptId) {
        data.DepartmentId = app.globalData.deptId;
      }
      if (app.globalData.via) {
        data.via = app.globalData.via;
      }
      return getConsultDocIndex(data);
    },
    handleGetFinDocList(data) {
      this.docList = data?.Doctor;
      this.therapistList = data.Therapist;
    },
    // 扫描二维码
    scanInfo() {
      if (!this.checkIsLogin()) return;
      // if (!this.checkIsAug()) return
      uni.scanCode({
        scanType: ['barCode', 'qrCode', 'datamatrix', 'pdf417'],
        success: (res) => {
          console.log('条码内容：' + res.result);
          if (res.result.includes('DeviceName')) {
            //如果扫的是设备的二维码
            const data = JSON.parse(res.result);
            this.checkIsBind(data.Data);
          } else if (res.result.includes('docId')) {
            //如果扫的是医生的二维码
            uni.navigateTo({
              url: '/subPackIndex/docDetail?docId=' + res.result.split('=')[1],
            });
          } else if (
            res.result.includes('orgId') &&
            res.result.includes('orgName')
          ) {
            let obj = getUrlParams(res.result);

            const { orgId, orgName } = obj;
            getApp().changeOrgAndMark({
              orgId,
              orgName,
            });
            this.initData();
          }
        },
        fail: (err) => {
          console.log('err', err);
        },
      });
    },
    //设备是否绑定
    async checkIsBind(info) {
      let data = {
        DeviceCode: info.DeviceCode,
        DeviceTypeCode: info.DeviceTypeCode,
        DeviceFactory: info.DeviceFactory,
        MenberId: app.globalData.userInfo.Id,
      };
      let res = await checkBindingUserDevices([data]);
      if (res.Type == 200) {
        if (res.Data) {
          this.bindThisDev(info);
        } else {
          this.showBind = true;
        }
      }
    },
    async bindThisDev(info) {
      let data = [
        {
          DeviceCode: info.DeviceCode,
          DeviceTypeCode: info.DeviceTypeCode,
          DeviceFactory: info.DeviceFactory,
          UserId: app.globalData.userInfo.Id,
          MenberId: app.globalData.userInfo.Id,
          Sex: app.globalData.userInfo.Sex,
          UserName: app.globalData.userInfo.Name,
        },
      ];
      if (app.globalData.userInfo.Birthday) {
        data[0].Birthday = app.globalData.userInfo.Birthday;
      }
      let res = await bindingUserDevices(data);
      if (res.Type == 200) {
        this.$refs.uToast.show({
          message: res.Content,
          type: 'success',
        });
      } else {
        this.$refs.uToast.show({
          message: res.Message || '绑定失败',
          type: 'error',
        });
      }
    },
    onSure() {
      this.showBind = false;
      uni.navigateTo({
        url: '/subPackIndex/user/myDevice',
      });
    },
    // 获取当前选中的机构信息
    async getChooseOrgInfo(id) {
      let res = await getOrganizationById(id);
      if (res.Type == 200) {
        this.orgInfo = res.Data;
        if (
          res.Data.OrganizationConsortiums &&
          res.Data.OrganizationConsortiums.length > 0
        ) {
          this.OrganizationConsortiumInfo.Name =
            res.Data.OrganizationConsortiums[0].ConsortiumName;
        }
        this.$nextTick(() => {
          const query = uni.createSelectorQuery().in(this);
          query
            .select('#swiperWidth')
            .boundingClientRect((data) => {
              this.swiperHeight = data.width * 0.39;
            })
            .exec();
        });
      }
    },
    // 刷新今日训练的会话未读数
    async refreshTrainingUnReadCount() {
      if (
        !dataIsValid(this.trainingInfo) ||
        !stringNotEmpty(this.trainingInfo.RoomId)
      )
        return;

      let trainingRoomId = this.trainingInfo.RoomId;
      let session = IMManager.instance
        .getSessionList()
        .find((e) => e.sessionId == trainingRoomId);
      if (session) {
        if (session.unreadCount != this.unReadCounts)
          this.unReadCounts = session.unreadCount;
        return;
      }

      if (!this.needRequestTrainningUnreadCount) return;
      let imApi = new IMApi();
      let userId = app.globalData.userInfo.Id;
      this.needRequestTrainningUnreadCount = false; // 放此处是为了方式，快速的多次请求
      let r = await imApi.getRoomUnreadCount(userId, [trainingRoomId]);
      if (r.isSuccess) {
        let unreadCount = r.data[trainingRoomId];
        if (this.unReadCounts != unreadCount) {
          this.unReadCounts = unreadCount;
        }
      }

      if (r.isFailure) {
        // 失败了可再请求
        this.needRequestTrainningUnreadCount = true;
      }
    },
    // 刷新有效咨询记录列表的会话未读数
    async refreshConsultsUnReadCount() {
      let that = this;
      let consultList = this.inquiryStatusList.filter(
        (e) => e.Id != that.trainingInfo.Id
      );
      if (!arrayNotEmpty(consultList)) return;

      var roomIds = [];
      // 先从会话列表中获取
      let sessions = IMManager.instance.getSessionList();
      consultList.forEach((consult) => {
        let session = sessions.find((s) => s.sessionId == consult.RoomId);
        if (session) {
          if (session.unreadCount != consult.unreadCount) {
            consult.unreadCount = session.unreadCount;
          }
        } else {
          roomIds.push(consult.RoomId);
        }
      });

      if (arrayNotEmpty(roomIds) && this.needRequestConsultUnreadCount) {
        // 未获取到，直接请求未读数
        this.needRequestConsultUnreadCount = false;
        let userId = app.globalData.userInfo.Id;
        let imApi = new IMApi();
        let r = await imApi.getRoomUnreadCount(userId, roomIds);
        if (r.isFailure) {
          this.needRequestConsultUnreadCount = true;
          uni.showToast({
            title: r.msg,
            icon: 'none',
          });
        }
        if (r.isSuccess) {
          Object.keys(r.data).forEach((roomId) => {
            var index = -1;
            let consult = consultList.find((c, i) => {
              let result = c.RoomId == roomId;
              if (result) index = i;
              return result;
            });

            let unreadCount = r.data[roomId];
            if (consult && consult.unreadCount != unreadCount) {
              consultList[index].unreadCount = unreadCount;
            }
          });
        }
      }
      // 重新组合，赋值展示
      if (consultList.length == this.inquiryStatusList) {
        this.inquiryStatusList = consultList;
      } else {
        this.inquiryStatusList = this.inquiryStatusList
          .slice(0, 1)
          .concat(consultList);
      }
    },
    // 获取宣教列表
    getContentByTypeList() {
      if (app.globalData.orgId) {
        this.query.OrgId = app.globalData.orgId;
      }
      if (app.globalData.userInfo.Id) {
        this.query.PatId = app.globalData.userInfo.Id;
      }
      return getFirstRecoveryMission(this.query);
    },
    handleGetContentByTypeList(data) {
      data.forEach((e, index) => {
        if (!e.ShowImg) {
          e.ShowImg = getMissionImageUrl(index);
        }
      });
      this.indexList = data;
    },
    //点击某个医生进行问诊
    async toWenZhen(item) {
      if (!this.checkIsLogin()) return;
      if (!this.checkIsAug()) return;
      uni.navigateTo({
        // url: `/subPackIndex/docDetail?itemInfo=${encodeURIComponent(JSON.stringify(item))}`
        url: `/subPackIndex/docDetail?docId=${item.Doctor.UserId}`,
      });
    },
    toPlan() {
      if (!this.checkIsLogin()) return;
      if (!this.checkIsAug()) return;
      uni.switchTab({
        url: '/pages/plan/index',
      });
    },
    // 查看更多医生
    toSeeDoc(type) {
      uni.navigateTo({
        url: `/subPackIndex/seeDoc?index=${type === 'doc' ? 0 : 1}`,
      });
    },
    // 取消去登录
    cancel() {
      this.show = false;
    },
    confirm() {
      this.show = false;
      this.userLogin();
    },
    // 手动去登陆
    userLogin() {
      uni.switchTab({
        url: '/pages/user/index',
      });
    },
    // 判断是否有登录信息,如果没有跳转登录页面
    checkIsLogin() {
      if (!app.isLoggedIn()) {
        app.openLoginPage();
        return false;
      } else {
        return true;
      }
    },
    // 判断是否认证
    checkIsAug() {
      if (app.globalData.userInfo.WorkflowStatus === 2) return true;
      this.showModal2 = true;
      return false;
    },
    // 微信一件登录
    async getPhoneNumber(e) {
      const r = await getApp().loginInWithWX(e);
      if (r) {
        this.show = false;
        // 初始化用户信息
        this.initData();
      }
    },
    async recordUser() {
      const data = {
        UserId: app.globalData.userInfo.Id || '', // 用户的Id
        OpenId: app.globalData.openId || '', // 用户的openID
        RelationId: '264cbffb-b8a8-443a-9f58-390f2e364021', // 存储的医生(或者治疗师)ID或者是医院的ID
        Type: 1, // 0：医生或者治疗师的 1:医院 2: 公众号
      };
      await initServer(data);
    },
    // 构建分享连接
    handleConstructionShare(orgId, orgName) {
      if (orgId && orgName) {
        getApp().changeOrgAndMark({
          orgId,
          orgName,
        });
        if (share) {
          uni.setStorageSync('shareObj', {
            RelationId: orgId,
            OrganizationId: orgId,
            Type: 5,
          });
        }
      }
    },
    /**
     * 获取当前机构的医联体信息
     */
    async onGetOrganizationConsortium(Id) {
      const res = await OrganizationConsortiumGet({
        ConsortiumId: Id,
        PageIndex: 1,
        PageSize: 99,
      });
      if (res.Type === 200) {
        this.OrganizationConsortiumInfo.List = res.Data.Data;
      }
    },
    async initData(orgId, orgName, share = false) {
      if (share == 'true') {
        app.globalData.orgId = orgId;
        app.globalData.orgName = orgName;
        uni.setStorageSync('chooseOrgID', orgId);
        uni.setStorageSync('chooseOrgName', orgName);
      }
      this.handleConstructionShare();
      if (app.globalData.orgId) {
        await this.getChooseOrgInfo(app.globalData.orgId);
        // 获取科室
        await this.getDeptList();
        // 获取当前医联体信息
        if (
          this.orgInfo.OrganizationConsortiums &&
          this.orgInfo.OrganizationConsortiums.length > 0
        ) {
          this.onGetOrganizationConsortium(
            this.orgInfo.OrganizationConsortiums[0].ConsortiumId
          );
        } else {
          this.OrganizationConsortiumInfo.List = [];
        }
        // 获取当前机构是否开启切换医院、风险预估
        this.onGetOrgDefaultData(app.globalData.orgId);
      }
      uni.showLoading({
        title: this.$loadingMsg,
      });
      // // 获取首页的医生不需要登录 获取首页宣教列表不需要登录
      const prpmoseData = await Promise.all([
        this.getFinDocList(),
        this.getContentByTypeList(),
      ]);
      console.log('prpmoseData', prpmoseData);
      this.handleGetFinDocList(prpmoseData[0].Data);
      this.handleGetContentByTypeList(prpmoseData[1].Data.Data);
      this.$nextTick(() => {
        uni.hideLoading();
      });
      if (app.globalData.userInfo.Id) {
        this.userInfo = app.globalData.userInfo;
        await this.getIndexTraning();
        if (this.option.showToDocReport) {
          this.onShowToReport();
        }
        // 防止用户在其他地方登录之后 再到首页 会显示让用户去登录
        this.show = false;
      } else {
        // 如果是河北省的小程序 需要记录新用户进入小程序
        if (config.resources == 'hbszyy') {
          this.$log.info('我是河北省小程序进来的,并且没有登录');
          this.recordUser();
        }
        if (this.option.type === 'WZ') {
          this.show = true;
        }
      }
    },
    async onGetOrgDefaultData(orgId) {
      const res = await onGetOrgDefault({
        OrgQueryItems: [
          {
            OrgId: orgId,
            Codes: ['SwitchOrganizationIcon', 'OldPeopleRiskWarn'],
          },
        ],
      });
      if (res.Type === 200 && res.Data) {
        this.orgDefaultConfig = res.Data[orgId];
      }
    },
    async getIndexTraning() {
      // 判断是否有需要续方的
      // 是否有正在进行的康复训练
      // 是否又已结束的康复训练
      const res = await getTrainingInfoForIndex({});
      if (res.Type === 200) {
        if (res.Data.EvaTip) {
          this.showRehabilitationFinish = true;
          this.tosubServicesId = res.Data.EvaTip.ProgramId;
        }
        if (res.Data.GetIsContinue && res.Data.GetIsContinue.Id) {
          this.showContinuedPrescription = true;
          this.ContinuedPrescriptionModal.content = `您的居家方案${res.Data.GetIsContinue.Name}已结束，是否需要申请延续治疗`;
          this.ContinuedPrescriptionModal.prescriptionId =
            res.Data.GetIsContinue.Id;
        }
        if (
          res.Data.GetTodayTrainingProgram &&
          res.Data.GetTodayTrainingProgram.Id
        ) {
          res.Data.GetTodayTrainingProgram.FinishRate = (
            res.Data.GetTodayTrainingProgram.FinishRate * 100
          ).toFixed(2);
          this.trainingInfo = res.Data.GetTodayTrainingProgram || {};
          this.todayTarget.label =
            res.Data.GetTodayTrainingProgram.TodayFinishCount +
            '/' +
            res.Data.GetTodayTrainingProgram.TodayShouldCount;
          this.todayTarget.data =
            (res.Data.GetTodayTrainingProgram.TodayFinishCount /
              res.Data.GetTodayTrainingProgram.TodayShouldCount) *
            100;
          if (
            !app.globalData.indexToPlan &&
            res.Data.GetTodayTrainingProgram.Id
          ) {
            const { scene } = getUserEnterOptions();
            if (scene !== 1001 && scene !== 1089) return;
            uni.reLaunch({
              url: '/pages/plan/index',
            });
            app.globalData.indexToPlan = true;
            return;
          }
        }
        // this.refreshTrainingUnReadCount()

        // this.onGetInquiryStatus()
        // 获取首页的consult接口
        this.getIndexConsult();
      }
    },
    async getIndexConsult() {
      const data = {
        GetMyConsults: this.querys,
      };
      const res = await getConsultInfoForIndex(data);
      if (res.Type === 200) {
        if (res.Data.RecentRemind) {
          const endTime = res.Data.RecentRemind.EndDate;
          this.reminderTime = this.$dateFormat(endTime, 'YYYY-MM-DD', false);
        }
        if (res.Data.GetPatNotOpera && res.Data.GetPatNotOpera.TotalCount > 0) {
          res.Data.GetPatNotOpera.Data[0].TypeName = [
            '',
            '量表',
            '宣教',
            '问卷',
          ][res.Data.GetPatNotOpera.Data[0].Type];
          res.Data.GetPatNotOpera.Data[0].TypeUrl = [
            '',
            '/static/images/indexLB.png',
            '/static/images/indexXJ.png',
            '/static/images/indexWJ.png',
          ][res.Data.GetPatNotOpera.Data[0].Type];
          this.GetPatNotOpera = {
            TotalCount: res.Data.GetPatNotOpera.TotalCount,
            info: res.Data.GetPatNotOpera.Data[0],
          };
        }
        let inquiryStatusList = [];
        if (
          res.Data.GetMyConsults &&
          res.Data.GetMyConsults.ItemArr.length > 0
        ) {
          res.Data.GetMyConsults.ItemArr.forEach((e) => {
            e.forEach((k) => {
              k.ShowState = 'Inquiry';
              k.showDate =
                k.State == 1
                  ? this.$dateFormat(k.ConsultDate, 'YYYY-MM-DD')
                  : this.$dateFormat(k.VistDate, 'YYYY-MM-DD');
              inquiryStatusList.push(k);
            });
          });
        }
        if (this.trainingInfo && this.trainingInfo.Id) {
          this.trainingInfo.ShowState = 'plan';
          inquiryStatusList.unshift(this.trainingInfo);
        }
        let that = this;
        this.$nextTick(() => {
          this.inquiryStatusList = inquiryStatusList;
          if (
            this.option.type === 'WZ' &&
            inquiryStatusList.length > 0 &&
            inquiryStatusList[0].ShowState === 'plan'
          ) {
            this.option = {};
            this.onContactDoctor();
          } else if (
            this.option.type === 'WZ' &&
            (inquiryStatusList.length === 0 ||
              inquiryStatusList[0].ShowState !== 'plan')
          ) {
            this.option = {};
            this.showWZModal = true;
          }
          that.sessionListChangedHandler();
        });
        if (
          res.Data.GetMyUnExecutePrescription &&
          res.Data.GetMyUnExecutePrescription.Id
        ) {
          // this.PrescriptionDocName = res.Data.GetMyUnExecutePrescription.CreatorName
          if (res.Data.GetMyUnExecutePrescription.Role === 'doctor') {
            // this.PrescriptionDocName += '医生'
            this.PrescriptionDocName = '医生';
          } else if (res.Data.GetMyUnExecutePrescription.Role === 'therapist') {
            // this.PrescriptionDocName += '治疗师'
            this.PrescriptionDocName = '治疗师';
          }
          this.$store.commit(
            'message/updatePrescriptionId',
            res.Data.GetMyUnExecutePrescription.Id
          );
        } else {
          this.consultId = res.Data.EvaTip;
          if (res.Data.EvaTip) {
            this.onShowToPJ = true;
          }
        }
      }
    },
    // 判断是否是从扫医院二维码或者治疗师进来的 如歌是则首页需要弹出让他去医院报到的弹框
    onShowToReport() {
      this.showToReport = true;
    },
    onConfirmShowToReport() {
      (this.showToReport = false), (this.option.showToDocReport = false);
      uni.navigateTo({
        url: '/subPackIndex/seeDoc?showToReport=true',
      });
    },
    onConfirmShowToReportFalse() {
      (this.showToReport = false), (this.option.showToDocReport = false);
    },
    // 问诊状态是否有带接诊和咨询中的问诊记录
    async onGetInquiryStatus() {
      let res = await getMyConsults(this.querys);
      let inquiryStatusList = [];
      if (res.Type == 200 && res.Data && res.Data.ItemArr.length > 0) {
        res.Data.ItemArr.forEach((e) => {
          e.forEach((k) => {
            k.ShowState = 'Inquiry';
            k.showDate =
              k.State == 1
                ? this.$dateFormat(k.ConsultDate, 'YYYY-MM-DD')
                : this.$dateFormat(k.VistDate, 'YYYY-MM-DD');
            inquiryStatusList.push(k);
          });
        });
      }
      if (this.trainingInfo && this.trainingInfo.Id) {
        this.trainingInfo.ShowState = 'plan';
        inquiryStatusList.unshift(this.trainingInfo);
      }
      this.$nextTick(() => {
        this.inquiryStatusList = inquiryStatusList;
        if (
          this.option.type === 'WZ' &&
          inquiryStatusList.length > 0 &&
          inquiryStatusList[0].ShowState === 'plan'
        ) {
          this.option = {};
          this.onContactDoctor();
        } else if (
          this.option.type === 'WZ' &&
          (inquiryStatusList.length === 0 ||
            inquiryStatusList[0].ShowState !== 'plan')
        ) {
          this.option = {};
          this.showWZModal = true;
        }
      });
    },
    // 检查点击的时候的处方方案的状态
    async onDetectionStatus() {
      let res = await checkApplyContinuePreState({
        programId: this.ContinuedPrescriptionModal.prescriptionId,
      });
      if (res.Type === 200) {
        this.onToContinuedPrescription();
      } else {
        if (res.Data) {
          // 查看处方
        } else {
          this.showContinuedPrescriptionModal = false;
          // 提示错误消息
          this.$refs.uToast.show({
            message: res.Content,
            type: 'error',
          });
        }
      }
    },
    async onToContinuedPrescription() {
      let res = await applyContinueRx({
        Id: this.ContinuedPrescriptionModal.prescriptionId,
      });
      if (res.Type === 200) {
        this.$refs.uToast.show({
          message: '续方申请提交成功',
          type: 'success',
        });
        this.showContinuedPrescriptionModal = false;
        this.showContinuedPrescription = false;
      } else {
        this.showContinuedPrescriptionModal = false;
        this.$refs.uToast.show({
          message: res.Content,
          type: 'error',
        });
      }
    },
    // 选择机构
    chooseOrgIdList() {
      uni.navigateTo({
        url: '/pages/index/chooseOrg?type=1',
      });
    },
    more() {
      uni.navigateTo({
        url: '/subPropaganda/index',
      });
    },
    pageScrooll(e) {
      this.showSty = e.detail.scrollTop > 100 ? true : false;
      this.showTransition = e.detail.scrollTop > 100 ? true : false;
    },
  },
  onPullDownRefresh() {
    console.log('下拉刷新');
    this.initData();
    uni.stopPullDownRefresh();
  },
  onShow() {
    this.checkWeakPassword();
  },
  onTabItemTap(e) {
    console.log('onTabItemTap', e);
    this.initData();
  },
  onPageScroll(e) {
    this.showSty = e.scrollTop > 100 ? true : false;
    this.showTransition = e.scrollTop > 100 ? true : false;
  },
  onUnload() {
    uni.$off(
      SessionClientEvent.sessionListChanged,
      this.sessionListChangedHandler
    );
    uni.$off(MessageClientEvent.messageReceived, this.messageReceived);
    uni.$off(
      PrescriptionClientEvent.payTreatSuccess,
      this.payTreatSuccessHandler
    );
  },
};
</script>

<style lang="scss" scoped>
.spinal-overlay-warp {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  &-container {
    width: 580rpx;
    height: 706rpx;
    background: linear-gradient(0deg, #ffffff 0%, #f8fffe 76%, #e2fffc 100%);
    box-shadow: 0rpx -2rpx 8rpx 0rpx rgba(41, 183, 163, 0.08);
    border-radius: 24rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 96rpx;
    &-banner {
      width: 322rpx;
      height: 314rpx;
      background-image: url('https://kfxoss-biz.oss-cn-hangzhou.aliyuncs.com/static/frontend/mp-patient/index-overlay-banner.png');
      background-size: cover;
      background-repeat: no-repeat;
      background-position: center center;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    &-text {
      font-weight: 400;
      font-size: 28rpx;
      color: #555555;
      margin-bottom: 20rpx;
    }
    &-button {
      width: 300rpx;
      height: 64rpx;
      background: #29b7a3;
      border-radius: 32rpx;
      font-weight: 600;
      font-size: 32rpx;
      color: #ffffff;
      line-height: 64rpx !important;
      margin-top: 46rpx;
    }
  }
}
.container {
  height: 100vh;
  background-color: #f7f7f7;
  position: relative;
  // /deep/ .swiper {
  // 	height: 300px;
  // }

  ::-webkit-scrollbar {
    display: none;
  }

  /deep/ .u-cell {
    background-color: white;
  }

  /deep/ .u-modal__content {
    // max-height: 1200rpx;
    overflow-y: auto;
    text-align: left !important;
  }

  &-prescription {
    position: fixed;
    top: 58%;
    width: 100px;
    height: 50px;
    right: 0;
    text-align: center;
    line-height: 50px;
    color: white;
    z-index: 999;
    border-radius: 40px;
    transform: translateX(20px);
    font-size: 16px;
    background: #ffffff;
    box-shadow: 5px 6px 18px 0px rgba(0, 0, 0, 0.16);
    border-radius: 62px 0px 0px 62px;
    color: #999999;
  }

  .all-box {
    transform: translateY(-100upx);

    /deep/ .insideStyle {
      padding: 0 32rpx;
    }
    &-spinal {
      width: 90%;
      margin: 0 auto;
      margin-top: 20rpx;
      height: 188rpx;
      background-image: url('https://kfxoss-biz.oss-cn-hangzhou.aliyuncs.com/static/frontend/mp-patient/index-adolescent-spine-development.png');
      background-size: cover;
      background-repeat: no-repeat;
      background-position: center center;
      position: relative;
      &-btn {
        width: 120rpx;
        height: 46rpx;
        background: #ffffff;
        border-radius: 24rpx;
        text-align: center;
        line-height: 46rpx;
        font-weight: 500;
        font-size: 24rpx;
        color: #29b7a3;
        position: absolute;
        right: 28rpx;
        top: 50%;
        transform: translateY(-50%);
      }
    }

    .swiper {
      .card-button {
        width: 162rpx;
        height: 56rpx;
        border-radius: 28rpx;
        border: 4rpx solid #29b7a3;
        z-index: 9999;
        position: absolute;
        right: 10px;
        top: 20px;
        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center;

        text {
          color: #29b7a3;
          font-size: 26rpx;
        }
      }
    }

    &-paddingStyle {
      width: 90% !important;
      margin: 0 auto;
    }

    &-docbox {
      background: white;
      padding-top: 20rpx;
      padding-bottom: 20rpx;
      border-radius: 20rpx;
      margin-top: 20rpx;
    }

    &-riskWarning {
      width: 90% !important;
      margin: 0 auto;
      margin-top: 28rpx;
      background: #ffffff;
      border-radius: 10rpx 10rpx 10rpx 10rpx;
      padding: 36rpx 20rpx;
      display: flex;
      align-items: center;
      justify-content: flex-start;

      &-right {
        margin-left: 18rpx;
        flex: 1;

        &-title {
          font-weight: 500;
          font-size: 34rpx;
          color: #323233;
          line-height: 42rpx;
        }

        &-described {
          font-weight: 400;
          font-size: 24rpx;
          color: rgba(108, 108, 108, 0.8);
          line-height: 30rpx;
          margin-top: 20rpx;
          display: block;
        }
      }
    }

    /deep/ .u-scroll-list {
      padding-bottom: 0 !important;
      width: 90% !important;
      margin: 0 auto;
    }

    .scroll-list {
      @include flex(column);

      &__goods-item {
        margin-right: 20px;

        &__image {
          width: 60px;
          height: 60px;
          border-radius: 4px;
        }

        &__text {
          color: #f56c6c;
          text-align: center;
          font-size: 12px;
          margin-top: 5px;
        }
      }

      &__show-more {
        background-color: #fff0f0;
        border-radius: 3px;
        padding: 3px 6px;
        @include flex(column);
        align-items: center;

        &__text {
          font-size: 12px;
          width: 12px;
          color: #f56c6c;
          line-height: 16px;
        }
      }
    }

    .container-doc-sq {
      width: 90% !important;
      margin: 0 auto;
      margin-top: 32rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;

      &-each {
        margin-bottom: 24rpx;
        background: #ffffff;
        box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(41, 183, 163, 0.1);
        border-radius: 24rpx;
        font-weight: 600;
        font-size: 32rpx;
        color: #333333;
        line-height: 44rpx;
        padding: 16rpx;
        width: 49%;
        position: relative;

        &-style {
          &-time {
            font-weight: 600;
            font-size: 24rpx;
            color: #666666;
            line-height: 34rpx;
            position: absolute;
            bottom: 10rpx;
            right: 50rpx;
          }
        }
      }
    }

    /deep/ .u-swiper__wrapper {
      height: none !important;
    }

    /deep/ .u-swiper__wrapper__item__wrapper {
      border-radius: 4px;
    }

    .indicator {
      @include flex(row);
      justify-content: center;

      &__dot {
        height: 6px;
        width: 6px;
        border-radius: 100px;
        background-color: rgba(255, 255, 255, 0.35);
        margin: 0 5px;
        transition: background-color 0.3s;

        &--active {
          background-color: #ffffff;
        }
      }
    }

    .indicator-num {
      padding: 2px 0;
      background-color: rgba(0, 0, 0, 0.35);
      border-radius: 100px;
      width: 35px;
      @include flex;
      justify-content: center;

      &__text {
        color: #ffffff;
        font-size: 12px;
      }
    }

    .all-box-news {
      padding: 10rpx;
      background-color: white;
      margin-top: 40rpx;
      box-shadow: -1px -1px 4px -2px rgba(0, 0, 0, 0.3) inset;
      border-radius: 12rpx;
    }

    .liangbao {
      border-radius: 8px;
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: white;
      position: relative;
      height: 210px;
      overflow: hidden;
      margin-top: 32rpx;

      .liangbao-text {
        position: absolute;
        z-index: 999;
        top: 20px;
        left: 20px;

        .liangbao-btn {
          width: 100px;
          height: 40px;
          text-align: center;
          line-height: 40px;
          border-radius: 20px;
          background-color: #29b7a3;
          margin-top: 25px;
          color: white;
        }
      }

      // background-image: url(../../static/images/MaskGroup.png);
    }

    /deep/ .u-list {
      height: auto !important;
    }

    /deep/ .u-list-item {
      background-color: white;
      margin-bottom: 18rpx;
      box-shadow: 2.5px 3px 9px 0px rgba(0, 0, 0, 0.08);
      border-radius: 12rpx;
    }

    /deep/ .u-cell__body {
      padding: 10px 0;
    }

    .doc-info-each {
      background-color: white;
      height: 120px;
      width: 260px;
      margin-right: 10px;
      border-radius: 24rpx;
      display: flex;
      align-items: center;
      box-shadow: 2.5px 3px 9px 0px rgba(0, 0, 0, 0.08);
      position: relative;

      .right-button {
        width: 80px;
        height: 24px;
        border-radius: 20px;
        text-align: center;
        line-height: 24px;
        background-color: #29b7a3;
        color: white;
        margin-top: 11px;
        font-size: 14px;
        margin-left: 54px;
        position: absolute;
        bottom: 12rpx;
        right: 24rpx;
      }

      .doc-info-each-right {
        position: relative;
        margin-left: 32rpx;
        transform: translateY(-10rpx);
      }
    }

    .title-left {
      font-size: 20px;
      font-weight: 700;
      margin-left: 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 0 auto;
      margin-top: 32rpx;
    }

    .dept-subsection {
      width: 90%;
      margin: 20rpx auto;
      background: rgba(27, 183, 161, 0.08);
      border-radius: 50rpx;
      padding: 0 20rpx 0 0;
    }

    .wenz-box1 {
      padding: 4px;
      height: 120px;
      transform: translateY(-8px);
      display: flex;
      justify-content: flex-start;
      align-items: center;
      background-color: white;
      margin-top: 12px;
      border-radius: 10px;

      .wenz-box1-right {
        flex: 1;
        text-align: center;
      }

      .orgDes {
        text-align: center;
        flex: 1;
        text-align: center;
        margin-left: 4px;
      }
    }

    .wenz-box {
      width: 100%;
      height: 140px;
      transform: translateY(-8px);
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 10px;

      .wenz-left {
        width: 48%;
        height: 100%;
        text-align: center;
        background-color: white;
        border-radius: 10px;
      }

      .wenz-right {
        width: 48%;
        height: 100%;
        text-align: center;
        background-color: white;

        .wenz-right-top {
          height: 60px;
          line-height: 60px;
          font-size: 18px;
          font-weight: 700;
          text-align: left;
          margin-left: 8px;
          display: flex;
          justify-content: space-around;
          align-items: center;
        }

        .wenz-right-bom {
          height: 60px;
          margin-top: 20px;
          line-height: 60px;
          font-size: 18px;
          font-weight: 700;
          text-align: left;
          margin-left: 8px;
          display: flex;
          justify-content: space-around;
          align-items: center;
        }
      }
    }

    .plan-box {
      width: 100%;
      background-color: white;
      border-radius: 8px;
      padding-top: 10rpx;
      margin-top: 10px;

      .card-button {
        width: 162rpx;
        height: 56rpx;
        border-radius: 28rpx;
        border: 4rpx solid #29b7a3;
        z-index: 9999;
        position: absolute;
        right: 10px;
        top: 20px;
        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center;

        text {
          color: #29b7a3;
          font-size: 26rpx;
        }
      }

      .day-pro {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 14px;
        text-align: center;
        width: 100%;
        transform: translateY(-24px);

        .day-pro-box {
          width: 50%;

          .fontColor {
            color: #29b7a3;
            font-size: 16px;
            font-weight: 600;
          }
        }
      }

      .charts-box {
        margin: 5px auto;
        display: flex;
        justify-content: center;
      }

      .plan-box-top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 14px;
      }
    }
  }

  .sty-userInfo {
    position: fixed;
    left: 0;
    z-index: 999999999;
    padding: 0 18px;
    width: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: white;

    .sty-userInfo-st {
      width: 100%;
      display: flex;
      align-items: center;
      position: absolute;
      bottom: 0;
    }
  }

  .cl-bg {
    width: 100%;
    height: 373upx;
    background-image: linear-gradient(to right, #62bf7e, #00aeb7);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 18px;

    .userInfo {
      display: flex;
      align-items: center;
      margin-top: 25px;
    }

    .icon-all {
      display: flex;
      justify-content: space-around;
      align-items: center;
      margin-top: 25px;
      position: relative;

      /deep/ .u-badge {
        background-color: red;
        position: absolute !important;
        top: 2px !important;
        right: 70px !important;
      }
    }
  }

  .org-info-each {
    background: #ffffff;
    box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(41, 183, 163, 0.1);
    border-radius: 24rpx;
    padding: 24rpx 32rpx;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    width: 404rpx;
    margin-right: 24rpx;

    &-name {
      font-weight: 600;
      font-size: 28rpx;
      color: #333333;
      line-height: 40rpx;
      margin-left: 32rpx;
      flex: 1;
    }

    &-img {
      width: 124rpx;
      border-radius: 50%;
      height: 124rpx;
    }
  }
}
</style>
