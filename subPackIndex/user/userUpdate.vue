<template>
  <view class="box">
    <u-cell :title="title" v-if="type == 'UserName' || type == 'NickName'">
      <u-input
        type="text"
        slot="right-icon"
        :placeholder="placeholder"
        border="none"
        v-model="data"
        inputAlign="right"
      >
      </u-input>
    </u-cell>
    <view class="" v-if="type == 'mmsz'">
      <u-cell title="旧密码">
        <u--input
          slot="right-icon"
          placeholder="请输入旧密码"
          border="none"
          v-model="ChangePassword.OldPassword"
          inputAlign="right"
        ></u--input>
      </u-cell>
      <u-cell title="新密码">
        <u--input
          slot="right-icon"
          placeholder="请输入新密码"
          border="none"
          v-model="ChangePassword.NewPassword"
          inputAlign="right"
          password
        ></u--input>
      </u-cell>
      <u-cell title="确认密码">
        <u--input
          slot="right-icon"
          placeholder="请再次输入新密码"
          border="none"
          v-model="ChangePassword.ConfirmNewPassword"
          inputAlign="right"
          password
        ></u--input>
      </u-cell>
    </view>
    <p
      style="font-size: 14px; color: gray; padding-left: 20upx"
      v-if="type == 'Name'"
    >
      用户名是账号的唯一凭证，只能修改一次
    </p>
    <u-image
      :showLoading="true"
      src="/static/images/phone.png"
      width="100px"
      height="100px"
      customStyle="margin:50px auto 10px"
      v-if="type == 'sjbd'"
    ></u-image>
    <view style="text-align: center" v-if="type == 'sjbd'">
      <p>手机号 {{ data }}</p>
      <p style="font-size: 14px; color: gray; margin-top: 20upx">
        更换后个人信息不变，下次可以使用新手机号登录
      </p>
    </view>

    <view v-if="type == 'smxx' && authentication.WorkflowStatus === 2">
      <u-cell title="姓名" :value="authentication.Name"></u-cell>
      <u-cell
        title="证件号码"
        :value="authentication.UserCertificates[0].CertificateValue"
      ></u-cell>
    </view>

    <view v-if="type == 'smxx' && authentication.WorkflowStatus != 2">
      <u-cell title="姓名">
        <u--input
          slot="right-icon"
          placeholder="请输入真实姓名"
          border="none"
          v-model="smQuery.Name"
          inputAlign="right"
        ></u--input>
      </u-cell>
      <u-cell title="证件号码">
        <u--input
          slot="right-icon"
          placeholder="请输入真实证件号码"
          border="none"
          v-model="smQuery.UserCertificates[0].CertificateValue"
          inputAlign="right"
        ></u--input>
      </u-cell>
    </view>

    <u-button
      v-if="type == 'smxx' && authentication.WorkflowStatus !== 2"
      @click="toauthen"
      loadingText="保存"
      type="success"
      shape="circle"
      text="保存"
      customStyle="width:90%;margin:0 auto;margin-top:10px"
      :loading="loading"
    ></u-button>

    <u-button
      v-if="type != 'mmsz' && type != 'sjbd' && type != 'smxx'"
      @click="save"
      loadingText="保存"
      type="success"
      shape="circle"
      text="保存"
      customStyle="width:90%;margin:0 auto;margin-top:10px"
      :loading="loading"
    ></u-button>
    <u-button
      v-if="type == 'mmsz'"
      @click="handlePasswordChange"
      loadingText="保存中"
      type="success"
      shape="circle"
      text="确认并重新登录"
      customStyle="width:90%;margin:0 auto;margin-top:10px"
      :loading="loading"
    ></u-button>
    <u-button
      v-if="type == 'sjbd'"
      @click="changePhone"
      type="success"
      shape="circle"
      text="更换手机号"
      customStyle="width:90%;margin:0 auto;margin-top:10px"
    ></u-button>
    <u-notify :message="message" ref="uNotify"></u-notify>
    <u-toast ref="uToast"></u-toast>
    <u-modal
      :show="showModel"
      :showCancelButton="true"
      @cancel="showModel = false"
      @close="showModel = false"
      @confirm="onChangePhone"
    >
      <u-input
        placeholder="请输入新的手机号"
        border="surround"
        clearable
        v-model="changeData.PhoneNumber"
        @change="onChangePhoneFinsh"
      ></u-input>
      <span style="color: red; text-align: left" v-if="errorMessage">{{
        errorMessage
      }}</span>
      <u-input
        placeholder="输入验证码"
        customStyle="marginTop:20rpx"
        v-model="changeData.VerifyCode"
      >
        <view slot="suffix">
          <u-button
            @click="getCode"
            :disabled="disabled"
            :text="text"
            type="success"
            size="mini"
          ></u-button>
        </view>
      </u-input>
      <span style="color: red; text-align: left" v-if="errorMessage1">{{
        errorMessage1
      }}</span>
    </u-modal>
  </view>
</template>

<script>
import { changePassword, changePhoneOrEmail } from '@/api/passport.js';
import { idCardGetBri, idCardGetSex, idCardTrue } from '@/utils/utils';
import { dateFormat } from '@/utils/validate.js';
import { automaticApproval } from '@/api/identity.js';
import {
  userUpdateInfo,
  saveUserAuthenticationAudit,
  getUserByIdCard,
  checkPhoneNumberExists,
  getLatestAuth,
} from '@/api/passport';
import { sendCaptcha } from '@/api/cloudInfra.sms';
const app = getApp();
export default {
  data() {
    return {
      errorMessage1: '',
      errorMessage: '',
      disabled: true,
      changeData: {
        Id: '',
        Type: 0,
        PhoneNumber: '',
        VerifyCode: '',
        VerifyCodeId: '',
      },
      text: '获取验证码',
      countdown: 5,
      showModel: false,
      type: '',
      data: '',
      loading: false,
      show: false,
      message: '',
      title: '',
      placeholder: '',
      isAuthen: '',
      authentication: {}, //实名信息
      ChangePassword: {
        OldPassword: '',
        NewPassword: '',
        ConfirmNewPassword: '',
      },
      smQuery: {
        UserId: app.globalData.userInfo.Id,
        Name: '',
        Sex: '',
        Birthday: '',
        UserCertificates: [
          {
            CertificateType: 'idCard',
            CertificateValue: '',
          },
        ],
      },
      isBackPage: false,
    };
  },
  onLoad(option) {
    this.type = option.type;
    this.data = option.data;
    this.changeData.Id = app.globalData.userInfo.Id;
    if (option.type == 'UserName') {
      this.title = '用户名';
      this.placeholder = '请输入用户名';
    } else if (option.type == 'NickName') {
      this.title = '昵称';
      this.placeholder = '请输入昵称';
    } else if (option.type == 'smxx') {
      uni.setNavigationBarTitle({
        title: '认证',
      });
      this.getAuthen();
    }
  },
  methods: {
    async onChangePhone() {
      const res = await changePhoneOrEmail(this.changeData);
      if (res.Type === 200) {
        setTimeout(() => {
          uni.reLaunch({
            url: '/pages/index/index',
          });
        }, 2000);
      } else {
        this.errorMessage1 = res.Content;
      }
    },
    onChangePhoneFinsh() {
      var phoneRegexp = /^1[3456789]\d{9}$/;
      if (phoneRegexp.test(this.changeData.PhoneNumber)) {
        this.disabled = false;
      } else {
        this.disabled = true;
      }
    },
    // 获取验证码
    async getCode() {
      var phoneRegexp = /^1[3456789]\d{9}$/;
      if (!phoneRegexp.test(this.changeData.PhoneNumber)) {
        this.errorMessage = '手机号格式错误';
        return;
      }
      this.errorMessage = '';
      const res1 = await checkPhoneNumberExists(this.changeData.PhoneNumber);
      if (res1.Data) {
        this.errorMessage = res1.Data ? '该手机号已注册过' : '';
        this.disabled = res1.Data;
        return;
      }
      const data = {
        phoneNumber: this.changeData.PhoneNumber,
        templateType: 'UpdatePhone',
      };
      const res = await sendCaptcha(data);
      if (res.Type === 200) {
        this.errorMessage1 = '验证码已发送';
        setTimeout(() => {
          this.errorMessage1 = '';
        }, 2000);
        this.changeData.VerifyCodeId = res.Data;
        this.disabled = true;
        this.text = `${this.countdown}秒后重新获取`;
        // 每隔一秒更新倒计时时间
        const timer = setInterval(() => {
          this.countdown--;
          this.text = `${this.countdown}秒后重新获取`;

          // 倒计时结束后恢复按钮状态
          if (this.countdown <= 0) {
            clearInterval(timer);
            this.text = '获取验证码';
            this.disabled = false;
            this.countdown = 60;
          }
        }, 1000);
      } else {
        this.errorMessage1 = res.Content;
      }
    },
    // 点击实名接口
    async toauthen() {
      if (
        !this.smQuery.Name ||
        !this.smQuery.UserCertificates[0].CertificateValue
      ) {
        this.$refs.uToast.show({
          message: '请输入姓名或者身份证号码',
          type: 'error',
        });
        return;
      }
      if (idCardTrue(this.smQuery.UserCertificates[0].CertificateValue)) {
        console.log('身份证正确');
        let res = await getUserByIdCard({
          idCard: this.smQuery.UserCertificates[0].CertificateValue,
        });
        console.log('res', res);
        if (res.Type == 200 && res.Data) {
          if (res.Data.Id !== app.globalData.userInfo.Id) {
            this.$refs.uToast.show({
              message: `你输入的证件号已在其他账号(${this.phoneHandel(
                res.Data.PhoneNumber
              )})认证，同一证件不能多次认证，如有疑问，请联系客服`,
              type: 'error',
            });
            return;
          }
          this.SaveAuthen();
        } else if (res.Type == 200 && !res.Data) {
          // 输入的身份证没有用过 并且输入框都输入了数据
          this.smQuery.Birthday = idCardGetBri(
            this.smQuery.UserCertificates[0].CertificateValue
          );
          this.smQuery.Sex = idCardGetSex(
            this.smQuery.UserCertificates[0].CertificateValue
          );
          this.SaveAuthen();
        }
      } else {
        console.log('身份证错误');
        this.$refs.uToast.show({
          message: '身份证格式输入错误',
          type: 'error',
        });
        return;
      }
    },
    // 实名认证
    async SaveAuthen() {
      let res = await saveUserAuthenticationAudit([this.smQuery]);
      if (res.Type == 200) {
        this.$refs.uToast.show({
          message: '认证审核中，请稍后',
          type: 'success',
        });
        this.isBackPage = true;
        // 系统自动审核通过 调这个接口获取用户最新的数据（主要是拿到用户认证的字段WorkflowStatus为2 现在是后端自动通过 不需要前端调接口了
        // this.getAutomaticApproval()
        this.getAuthen();
      } else {
        this.$refs.uToast.show({
          message: res.Content,
          type: 'error',
        });
      }
    },
    async getAutomaticApproval() {
      let res = await automaticApproval({
        UserId: app.globalData.userInfo.Id,
      });
      if (res.Type == 200) {
        this.getAuthen();
      }
    },
    // 手机号脱敏
    phoneHandel(num) {
      if (num) {
        return num.substring(0, 3) + '****' + num.substring(num.length - 4);
      } else {
        return false;
      }
    },
    // 获取患者是否实名
    async getAuthen() {
      let res = await getLatestAuth({
        userId: app.globalData.userInfo.Id,
        status: [0, 2],
      });
      if (res.Type == 200) {
        if (res.Data.Status != 2) {
          this.smQuery.Name = res.Data.Name;
          return;
        }
        res.Data.WorkflowStatus = res.Data.Status;
        delete res.Data.Status;
        this.authentication = res.Data;
        this.isAuthen =
          res.Data.WorkflowStatus == 2
            ? '已实名'
            : res.Data.WorkflowStatus == 0 || res.Data.WorkflowStatus == 1
              ? '认证中'
              : '未认证';
        res.Data.Age =
          new Date().getFullYear() -
          dateFormat(res.Data.Birthday, 'YYYY-MM-DD').split('-')[0];
        app.globalData.userInfo = res.Data;
        uni.setStorageSync('userInfoLoging', res.Data);
        if (this.isBackPage) {
          let pages = getCurrentPages();
          const prePage = pages[pages.length - 2];
          if (pages.length == 2 && prePage.$vm.certificationCompletion) {
            uni.navigateBack({
              delta: 1,
              success: () => {
                prePage.$vm.certificationCompletion();
              },
            });
            return;
          }
          uni.navigateBack();
        }
      }
    },
    async save() {
      this.loading = true;
      let obj = {};
      if (this.type == 'UserName') {
        obj.UserName = this.data;
      } else if (this.type == 'NickName') {
        obj.NickName = this.data;
      }
      let rsp = await userUpdateInfo(obj);
      this.loading = false;
      if (rsp.Type != 200) {
        this.$refs.uNotify.warning(rsp.Content);
      } else {
        this.$refs.uNotify.primary('修改成功');
        let copyData = uni.getStorageSync('userInfoLoging');
        console.log('copyData', copyData);
        copyData[this.type] = this.data;
        app.globalData.userInfo = copyData;
        uni.removeStorage('userInfoLoging');
        uni.setStorageSync('userInfoLoging', copyData);
        setTimeout(() => {
          uni.navigateBack();
        }, 1000);
      }
    },
    async handlePasswordChange() {
      if (this.ChangePassword.NewPassword == this.ChangePassword.OldPassword) {
        this.$refs.uNotify.warning('新密码不能和旧密码一样');
        return;
      }
      if (
        this.ChangePassword.NewPassword !=
        this.ChangePassword.ConfirmNewPassword
      ) {
        this.$refs.uNotify.warning('新密码和确认密码不相同');
        return;
      }
      this.loading = true;
      let res = await changePassword(this.ChangePassword);
      this.loading = false;
      if (res.Type != 200) {
        this.$refs.uNotify.warning(rsp.Content);
      } else {
        this.$refs.uNotify.primary('修改成功');
        uni.removeStorageSync('token');
        uni.removeStorageSync('userInfoLoging');
        app.globalData.userInfo = {};
        setTimeout(() => {
          uni.reLaunch({
            url: '../../pages/user/index',
          });
        }, 1000);
      }
    },
    //切换手机号
    changePhone() {
      this.showModel = true;
      // this.$refs.uNotify.warning('暂不支持在小程序更换手机号，请前往app')
    },
  },
};
</script>

<style scoped lang="scss">
.box {
  height: 100vh;
  background-color: #f7f7f7;
  padding-top: 30upx;

  /deep/ .u-modal__content {
    flex-direction: column !important;
  }

  /deep/ .u-cell {
    background-color: white !important;
  }

  /deep/ .u-cell__body__content {
    flex: none !important;
  }

  /deep/ .u-cell__value {
    flex: 1 !important;
    text-align: right;
  }

  /deep/ .u-cell__right-icon-wrap {
    flex: 1 !important;
    text-align: right;
  }
}
</style>
