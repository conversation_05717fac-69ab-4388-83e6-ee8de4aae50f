<template>
  <div class="container">
    <view class="container-search">
      <u-search
        v-model="searchKey"
        :showAction="true"
        actionText="搜索"
        @custom="search"
        :animation="false"
      >
      </u-search>
    </view>

    <view v-for="(item, index) in list" :key="item.Id">
      <u-cell iconStyle="fontSize: 28px" @click="onChoose(item)">
        <p slot="title" style="margin-left: 10px">{{ item.InviterName }}</p>
      </u-cell>
    </view>
    <u-toast ref="uToast"></u-toast>
    <u-loadmore
      :status="status"
      @loadmore="getMoreList"
      loadmoreText="点我加载更多"
    />
  </div>
</template>

<script>
const app = getApp();
import { getInviterList, BindUserScanQR } from '@/api/passport.js';
export default {
  data() {
    return {
      searchKey: '',
      list: [],
      status: 'nomore',
      pageIndex: 1,
    };
  },
  onLoad() {
    this.getList();
  },
  methods: {
    search() {
      const isFlag = uni.$inputValueRegExp.test(this.searchKey);
      if (!isFlag) {
        uni.showToast({
          title: this.$errorInputValue,
          icon: 'none',
        });
        return;
      }
      this.pageIndex = 1;
      this.status = 'nomore';
      this.list = [];
      this.getList();
    },
    async getList() {
      const par = {
        pageIndex: this.pageIndex,
        pageSize: 20,
        keyword: this.searchKey,
      };
      const res = await getInviterList(par);
      if (res.Type === 200) {
        if (res.Data.Data.length > 0) {
          res.Data.Data.forEach((v) => {
            this.list.push(v);
          });
          if (res.Data.Data.length < 20) {
            this.status = 'nomore';
          } else {
            this.status = 'loadmore';
          }
        } else {
          this.status = 'nomore';
        }
      }
    },
    getMoreList() {
      this.pageIndex++;
      this.getList();
    },
    async onChoose(item) {
      const data = {
        UserId: app.globalData.userInfo.Id,
        OpenId: app.globalData.openId,
        OrganizationId: item.OrganizationId,
        RelationId: item.Id,
        Type: 3,
      };
      const res = await BindUserScanQR(data);
      if (res.Type == 200) {
        this.$refs.uToast.show({
          message: res.Content,
          type: 'success',
        });
        setTimeout(() => {
          uni.navigateBack();
        }, 2000);
      } else {
        this.$refs.uToast.show({
          message: res.Content,
          type: 'error',
        });
      }
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  &-search {
    background-color: white;
    padding: 20rpx;
    z-index: 999;
  }

  /deep/ .u-cell__body {
    background-color: white;
  }
}
</style>
