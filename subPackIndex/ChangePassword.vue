<template>
  <view class="change-password">
    <!-- 注意，如果需要兼容微信小程序，最好通过setRules方法设置rules规则 -->
    <u-form labelWidth="auto" :model="model" ref="uForm">
      <u-form-item label="手机号" prop="phone" borderBottom required>
        <u-input v-model="model.phone" border="none" readonly></u-input>
      </u-form-item>
      <u-form-item label="验证码" prop="code" borderBottom required>
        <u--input
          v-model="model.code"
          border="none"
          placeholder="请填写验证码"
          clearable
          maxlength="4"
        ></u--input>
        <u-button
          slot="right"
          @tap="handleSendCode"
          :text="countDown > 0 ? `${countDown}秒后重新获取` : '获取验证码'"
          type="success"
          size="mini"
          :disabled="sendCodeDisabled || countDown > 0"
        ></u-button>
      </u-form-item>
      <u-form-item label="新密码" prop="password" borderBottom required>
        <u-input
          v-model="model.password"
          placeholder="请输入新密码"
          border="none"
          clearable
          maxlength="12"
          :type="passwordVisible ? 'text' : 'password'"
        >
          <template slot="suffix">
            <u-icon
              :name="passwordVisible ? 'eye' : 'eye-off'"
              :color="passwordVisible ? '#2979ff' : '#ccc'"
              size="24"
              @click="passwordVisible = !passwordVisible"
            ></u-icon>
          </template>
        </u-input>
      </u-form-item>
      <u-form-item
        label="确认新密码"
        prop="confirmPassword"
        borderBottom
        required
      >
        <u-input
          v-model="model.confirmPassword"
          placeholder="请再次输入新密码"
          border="none"
          clearable
          maxlength="12"
          :type="confirmPasswordVisible ? 'text' : 'password'"
        >
          <template slot="suffix">
            <u-icon
              :name="confirmPasswordVisible ? 'eye' : 'eye-off'"
              :color="confirmPasswordVisible ? '#2979ff' : '#ccc'"
              size="24"
              @click="confirmPasswordVisible = !confirmPasswordVisible"
            ></u-icon>
          </template>
        </u-input>
      </u-form-item>
    </u-form>

    <u-button
      type="primary"
      text="确定"
      shape="circle"
      customStyle="width:90%;bottom: 30px;position: fixed; left: 50%;transform: translateX(-50%);fontSize:16px;z-index:99"
      @click="handleSubmit"
    >
    </u-button>
  </view>
</template>

<script>
import { checkPhoneNumberExists, changePassword } from '../api/passport';
import { sendCaptcha } from '../api/cloudInfra.sms';
import ProtectionManager from '../services/ProtectionManager';

export default {
  data() {
    return {
      model: {
        phone: '',
        code: '',
        password: '',
        confirmPassword: '',
      },
      rules: {
        phone: {
          type: 'string',
          required: true,
          validator: (rule, value, callback) => {
            if (!value) {
              callback(new Error('请输入手机号'));
            } else {
              callback();
            }
          },
          trigger: 'blur',
        },
        code: {
          type: 'string',
          required: true,
          validator: (rule, value, callback) => {
            if (!value) {
              callback(new Error('请输入验证码'));
            } else {
              callback();
            }
          },
          trigger: 'blur',
        },

        password: {
          type: 'string',
          required: true,
          validator: (rule, value, callback) => {
            if (!value) {
              callback(new Error('请输入密码'));
            } else {
              if (!ProtectionManager.validatePassword(value)) {
                callback(new Error(ProtectionManager.tipMessage));
              }
              if (this.model.confirmPassword !== '') {
                this.$refs.uForm.validateField('confirmPassword');
              }
              callback();
            }
          },
          trigger: 'blur',
        },

        confirmPassword: {
          type: 'string',
          required: true,
          validator: (rule, value, callback) => {
            if (!value) {
              callback(new Error('请再次输入密码'));
            } else if (value !== this.model.password) {
              callback(new Error('两次输入密码不一致!'));
            } else {
              callback();
            }
          },
          trigger: 'blur',
        },
      },
      passwordRuleTip: '',
      codeSessionId: '',
      countDown: 0,
      sendCodeDisabled: false,
      passwordVisible: false,
      confirmPasswordVisible: false,
    };
  },
  onReady() {
    //如果需要兼容微信小程序，并且校验规则中含有方法等，只能通过setRules方法设置规则。
    this.$refs.uForm.setRules(this.rules);
    this.model.phone = getApp().globalData.userInfo.PhoneNumber;
  },
  methods: {
    async handleSendCode() {
      const phone = this.model.phone;
      if (!phone) {
        uni.showToast({
          title: '请输入手机号',
          icon: 'none',
        });
        return;
      }

      this.sendCodeDisabled = true;
      const r = await checkPhoneNumberExists(phone);
      if (r.Type != 200) {
        uni.showToast({
          title: r.Content,
          icon: 'none',
        });
        this.sendCodeDisabled = false;
        return;
      }
      if (!r.Data) {
        uni.showToast({
          title: '手机号未注册',
          icon: 'none',
        });
        this.sendCodeDisabled = false;
        return;
      }

      const r1 = await sendCaptcha({
        phoneNumber: phone,
        templateType: 'UpdatePwd',
      });
      this.sendCodeDisabled = false;

      if (r1.Type != 200) {
        uni.showToast({
          title: r1.Content,
          icon: 'none',
        });
        return;
      }
      this.codeSessionId = r1.Data;
      uni.showToast({
        title: '验证码发送成功',
        icon: 'none',
      });
      this.countDown = 60;
      const timer = setInterval(() => {
        this.countDown = this.countDown - 1;
        if (this.countDown <= 0) {
          clearInterval(timer);
        }
      }, 1000);
    },
    async handleSubmit() {
      try {
        const valid = await this.$refs.uForm.validate();
        if (!valid) {
          console.log('验证失败');
          return;
        }
      } catch (error) {
        uni.showToast({
          title: error[0].message,
          icon: 'none',
        });
        return;
      }

      const { phone, code, password, confirmPassword } = this.model;

      uni.showLoading({
        title: '密码修改中...',
        mask: true,
      });
      // const r = await new Promise((resolve) =>
      //   setTimeout(() => {
      //     resolve({
      //       Type: 400,
      //       Data: '',
      //       Content: '密码修改失败',
      //     });
      //   }, 1000)
      // );
      const r = await changePassword({
        sessionId: this.codeSessionId,
        captcha: code,
        newPassword: password,
      });
      uni.hideLoading();
      if (r.Type != 200) {
        uni.showToast({
          title: r.Content,
          icon: 'none',
        });
        return;
      }

      ProtectionManager.reset();

      uni.showToast({
        title: '密码修改成功',
        icon: 'none',
      });

      setTimeout(() => {
        uni.reLaunch({
          url: '/pages/index/index',
        });
      }, 1000);
    },
  },
};
</script>

<style>
.change-password {
  padding: 32rpx;
  background-color: white;
}
</style>
